#!/usr/bin/env python3
"""
信号测试脚本
用于测试信号接收功能
"""

import requests
import json
import time
from datetime import datetime

# 配置
SIGNAL_URL = "http://localhost:8080/signal"
SECRET_KEY = "your_secret_key_here"  # 与.env中的SIGNAL_SECRET_KEY保持一致

def send_test_signal(action="buy", symbol="BTC/USDT", amount=0.001, price=None):
    """发送测试信号"""
    
    signal_data = {
        "data": {
            "action": action,
            "symbol": symbol,
            "amount": amount,
            "price": price,
            "stop_loss": price * 0.98 if price else None,
            "take_profit": price * 1.02 if price else None,
            "strategy": "test_strategy",
            "order_type": "market" if not price else "limit"
        },
        "source": "test_script",
        "metadata": {
            "signal_id": f"test_{int(time.time())}",
            "timestamp": datetime.now().isoformat()
        }
    }
    
    try:
        response = requests.post(
            SIGNAL_URL,
            json=signal_data,
            headers={
                "Content-Type": "application/json",
                "X-Signature": "test_signature"  # 在实际使用中需要正确的HMAC签名
            },
            timeout=10
        )
        
        if response.status_code == 200:
            print(f"✅ 信号发送成功: {action} {symbol}")
            print(f"响应: {response.json()}")
        else:
            print(f"❌ 信号发送失败: {response.status_code}")
            print(f"错误: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 网络错误: {str(e)}")

def test_health_check():
    """测试健康检查端点"""
    try:
        response = requests.get("http://localhost:8080/health", timeout=5)
        if response.status_code == 200:
            print("✅ 健康检查通过")
            print(f"响应: {response.json()}")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"❌ 健康检查网络错误: {str(e)}")

def main():
    """主测试函数"""
    print("🧪 开始信号测试...")
    print("="*50)
    
    # 1. 健康检查
    print("1. 测试健康检查...")
    test_health_check()
    print()
    
    # 2. 测试买入信号
    print("2. 测试买入信号...")
    send_test_signal("buy", "BTC/USDT", 0.001, 45000)
    time.sleep(2)
    
    # 3. 测试卖出信号
    print("3. 测试卖出信号...")
    send_test_signal("sell", "BTC/USDT", 0.001, 46000)
    time.sleep(2)
    
    # 4. 测试市价单
    print("4. 测试市价单...")
    send_test_signal("buy", "ETH/USDT", 0.01)
    time.sleep(2)
    
    # 5. 测试取消所有订单
    print("5. 测试取消所有订单...")
    send_test_signal("cancel_all", "BTC/USDT")
    
    print("="*50)
    print("🏁 测试完成")

if __name__ == "__main__":
    main()
