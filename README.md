# 🤖 加密货币自动交易机器人

一个功能强大的加密货币自动交易机器人，支持多交易所、外部信号接收、智能风险管理和Telegram实时通知。

## ✨ 主要特性

- 🏪 **多交易所支持**: Binance、OKX、Bybit等主流交易所
- 📡 **外部信号接收**: 支持Webhook和TradingView信号
- 🎯 **智能策略管理**: 基于信号的交易策略
- ⚠️ **风险管理**: 止损止盈、仓位管理、每日亏损限制
- 📱 **Telegram通知**: 实时交易通知和每日摘要
- 📊 **现货和期货**: 支持现货和期货合约交易
- 🔒 **安全可靠**: HMAC签名验证、环境变量配置

## 🚀 快速开始

### 1. 环境要求

- Python 3.8+
- pip 或 conda

### 2. 安装依赖

```bash
# 克隆项目
git clone <repository-url>
cd crypto-trading-bot

# 安装依赖
pip install -r requirements.txt
```

### 3. 配置设置

#### 3.1 复制环境变量模板

```bash
cp .env.example .env
```

#### 3.2 编辑 `.env` 文件

```bash
# 交易所API配置
BINANCE_API_KEY=your_binance_api_key
BINANCE_SECRET=your_binance_secret
BINANCE_SANDBOX=true

OKX_API_KEY=your_okx_api_key
OKX_SECRET=your_okx_secret
OKX_PASSPHRASE=your_okx_passphrase
OKX_SANDBOX=true

# Telegram配置
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id

# 信号接收配置
SIGNAL_PORT=8080
SIGNAL_SECRET_KEY=your_secret_key_for_webhook_verification

# 交易配置
DEFAULT_EXCHANGE=binance
MAX_POSITION_SIZE=1000
RISK_PERCENTAGE=2.0
```

#### 3.3 配置文件说明

- `config/config.yaml`: 主要配置文件
- `config/exchanges.yaml`: 交易所配置
- `.env`: 敏感信息配置（API密钥等）

### 4. 启动机器人

```bash
# 启动交易机器人
python src/main.py

# 测试Telegram连接
python src/main.py --test-telegram

# 查看机器人状态
python src/main.py --status

# 手动交易
python src/main.py --manual-trade buy BTC/USDT 0.001
```

## 📡 信号接收

### HTTP Webhook

机器人启动后会在指定端口（默认8080）监听HTTP请求：

```bash
# 基础信号端点
POST http://localhost:8080/signal

# TradingView专用端点
POST http://localhost:8080/signal/tradingview

# 健康检查
GET http://localhost:8080/health
```

### 信号格式

```json
{
  "data": {
    "action": "buy",
    "symbol": "BTC/USDT",
    "amount": 0.001,
    "price": 45000,
    "stop_loss": 44000,
    "take_profit": 46000,
    "strategy": "my_strategy",
    "order_type": "market"
  },
  "source": "tradingview",
  "metadata": {
    "signal_id": "unique_id",
    "timestamp": "2024-01-01T12:00:00Z"
  }
}
```

### 支持的动作

- `buy`: 买入
- `sell`: 卖出
- `close_long`: 平多仓
- `close_short`: 平空仓
- `cancel_all`: 取消所有订单

## 🎯 策略配置

### 信号策略配置

```yaml
strategies:
  signal_strategy:
    enabled: true
    signal_timeout: 300
    min_confidence: 0.5
    max_signals_per_hour: 10
    enable_signal_filtering: true
    price_deviation_threshold: 5.0
    volume_threshold: 1000
```

## ⚠️ 风险管理

### 风险控制参数

```yaml
risk_management:
  max_daily_loss: 500.0
  max_position_size: 1000.0
  default_risk_percentage: 2.0
  max_open_positions: 5
  enable_stop_loss: true
  enable_take_profit: true
```

### 风险管理功能

- **每日亏损限制**: 达到限制后停止交易
- **仓位大小控制**: 基于账户余额和风险百分比
- **止损止盈**: 自动设置止损和止盈订单
- **最大持仓数量**: 限制同时持有的仓位数量

## 📱 Telegram通知

### 通知类型

- 📝 订单创建通知
- ✅ 交易完成通知
- 📡 信号接收通知
- 🚨 错误和风险警报
- 📊 每日交易摘要

### 配置Telegram Bot

1. 与 @BotFather 对话创建机器人
2. 获取Bot Token
3. 获取Chat ID（可以与 @userinfobot 对话获取）
4. 在 `.env` 文件中配置相关信息

## 🔧 高级配置

### 交易所配置

```yaml
exchanges:
  binance:
    ccxt_id: binance
    fees:
      spot:
        maker: 0.001
        taker: 0.001
      futures:
        maker: 0.0002
        taker: 0.0004
    popular_pairs:
      - BTC/USDT
      - ETH/USDT
      - BNB/USDT
```

### 订单管理配置

```yaml
orders:
  order_timeout: 300
  retry_attempts: 3
  price_offset_percentage: 0.1
  cancel_orders_on_stop: false
```

## 📊 监控和日志

### 日志文件

- `logs/trading.log`: 主要交易日志
- `logs/error.log`: 错误日志
- `logs/signal.log`: 信号处理日志

### 状态监控

```bash
# 查看实时状态
python src/main.py --status

# 查看日志
tail -f logs/trading.log
```

## 🛡️ 安全建议

1. **API权限**: 只授予必要的交易权限，不要开启提现权限
2. **沙盒测试**: 先在沙盒环境测试
3. **资金管理**: 不要投入超过承受能力的资金
4. **密钥安全**: 妥善保管API密钥和私钥
5. **网络安全**: 使用HTTPS和签名验证

## 🔍 故障排除

### 常见问题

1. **连接失败**
   - 检查API密钥是否正确
   - 确认网络连接正常
   - 验证交易所API权限

2. **信号不响应**
   - 检查信号接收端口是否开放
   - 验证信号格式是否正确
   - 查看日志文件排查错误

3. **Telegram通知失败**
   - 确认Bot Token和Chat ID正确
   - 检查网络连接
   - 验证机器人权限

### 调试模式

```bash
# 启用调试日志
export LOG_LEVEL=DEBUG
python src/main.py
```

## 📈 性能优化

- 使用连接池减少API调用延迟
- 合理设置订单超时时间
- 优化信号处理频率
- 定期清理历史数据

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## ⚠️ 免责声明

本软件仅供教育和研究目的使用。加密货币交易存在高风险，可能导致资金损失。使用本软件进行实际交易的风险由用户自行承担。开发者不对任何交易损失负责。

## 📞 支持

如有问题或建议，请：

1. 查看文档和FAQ
2. 搜索已有的Issues
3. 创建新的Issue
4. 联系开发团队

---

**⚡ 开始您的自动化交易之旅！**
