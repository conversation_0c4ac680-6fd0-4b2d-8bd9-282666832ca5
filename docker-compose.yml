version: '3.8'

services:
  crypto-trading-bot:
    build: .
    container_name: crypto-trading-bot
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
      - ./.env:/app/.env
    environment:
      - PYTHONPATH=/app
      - LOG_LEVEL=INFO
    networks:
      - trading-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  trading-network:
    driver: bridge

volumes:
  logs:
  config:
