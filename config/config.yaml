# 主配置文件
trading:
  # 默认交易所
  default_exchange: "binance"
  
  # 默认计价货币
  default_quote_currency: "USDT"
  
  # 最大仓位大小 (USDT)
  max_position_size: 1000
  
  # 风险管理
  risk_management:
    max_risk_per_trade: 2.0  # 每笔交易最大风险百分比
    max_daily_loss: 5.0      # 每日最大亏损百分比
    max_open_positions: 5    # 最大同时持仓数量
    stop_loss_percentage: 3.0 # 默认止损百分比
    take_profit_percentage: 6.0 # 默认止盈百分比

  # 订单配置
  orders:
    default_order_type: "limit"  # limit, market
    price_offset_percentage: 0.1  # 限价单价格偏移百分比
    order_timeout: 300           # 订单超时时间(秒)
    retry_attempts: 3            # 重试次数

# 信号接收配置
signals:
  # HTTP服务器配置
  server:
    host: "0.0.0.0"
    port: 8080
    secret_key: "your_secret_key_here"
  
  # 支持的信号类型
  supported_actions:
    - "buy"
    - "sell" 
    - "close_long"
    - "close_short"
    - "cancel_all"

# 通知配置
notifications:
  telegram:
    enabled: true
    send_on_order_create: true
    send_on_order_fill: true
    send_on_order_cancel: true
    send_on_error: true
    send_daily_summary: true

# 日志配置
logging:
  level: "INFO"
  file: "logs/trading.log"
  max_file_size: "10MB"
  backup_count: 5
  console_output: true

# 数据库配置 (可选)
database:
  enabled: false
  type: "sqlite"
  path: "data/trading.db"
