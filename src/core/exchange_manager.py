"""
交易所管理模块
负责管理多个交易所的连接和操作
"""

import ccxt
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
import time

from ..utils.logger import get_logger
from ..utils.helpers import load_env_config, safe_float

logger = get_logger()


class ExchangeManager:
    """交易所管理器"""
    
    def __init__(self, exchange_config: Dict, env_config: Dict):
        self.exchange_config = exchange_config
        self.env_config = env_config
        self.exchanges: Dict[str, ccxt.Exchange] = {}
        self.exchange_info: Dict[str, Dict] = {}
        
        # 初始化支持的交易所
        self._initialize_exchanges()
        
        logger.info(f"交易所管理器初始化完成，支持 {len(self.exchanges)} 个交易所")
    
    def _initialize_exchanges(self):
        """初始化交易所连接"""
        for exchange_id, config in self.exchange_config['exchanges'].items():
            try:
                exchange = self._create_exchange(exchange_id, config)
                if exchange:
                    self.exchanges[exchange_id] = exchange
                    self.exchange_info[exchange_id] = config
                    logger.info(f"成功初始化交易所: {exchange_id}")
            except Exception as e:
                logger.error(f"初始化交易所失败 {exchange_id}: {str(e)}")
    
    def _create_exchange(self, exchange_id: str, config: Dict) -> Optional[ccxt.Exchange]:
        """创建交易所实例"""
        ccxt_id = config['ccxt_id']
        
        # 获取API凭证
        api_credentials = self._get_api_credentials(exchange_id)
        if not api_credentials:
            logger.warning(f"未找到 {exchange_id} 的API凭证")
            return None
        
        # 创建交易所实例
        exchange_class = getattr(ccxt, ccxt_id)
        exchange_params = {
            'apiKey': api_credentials.get('api_key'),
            'secret': api_credentials.get('secret'),
            'sandbox': api_credentials.get('sandbox', True),
            'enableRateLimit': True,
            'timeout': 30000,
        }
        
        # 添加特殊参数
        if exchange_id == 'okx' and api_credentials.get('passphrase'):
            exchange_params['password'] = api_credentials['passphrase']
        
        exchange = exchange_class(exchange_params)
        
        # 测试连接
        try:
            exchange.load_markets()
            return exchange
        except Exception as e:
            logger.error(f"测试连接失败 {exchange_id}: {str(e)}")
            return None
    
    def _get_api_credentials(self, exchange_id: str) -> Optional[Dict]:
        """获取API凭证"""
        credentials = {}
        
        if exchange_id == 'binance':
            credentials = {
                'api_key': self.env_config.get('BINANCE_API_KEY'),
                'secret': self.env_config.get('BINANCE_SECRET'),
                'sandbox': self.env_config.get('BINANCE_SANDBOX', True)
            }
        elif exchange_id == 'okx':
            credentials = {
                'api_key': self.env_config.get('OKX_API_KEY'),
                'secret': self.env_config.get('OKX_SECRET'),
                'passphrase': self.env_config.get('OKX_PASSPHRASE'),
                'sandbox': self.env_config.get('OKX_SANDBOX', True)
            }
        
        # 检查必需的凭证
        if not credentials.get('api_key') or not credentials.get('secret'):
            return None
        
        return credentials
    
    def get_exchange(self, exchange_id: str) -> Optional[ccxt.Exchange]:
        """获取交易所实例"""
        return self.exchanges.get(exchange_id)
    
    def get_available_exchanges(self) -> List[str]:
        """获取可用的交易所列表"""
        return list(self.exchanges.keys())
    
    async def get_balance(self, exchange_id: str) -> Optional[Dict]:
        """获取账户余额"""
        exchange = self.get_exchange(exchange_id)
        if not exchange:
            return None
        
        try:
            balance = exchange.fetch_balance()
            return balance
        except Exception as e:
            logger.error(f"获取余额失败 {exchange_id}: {str(e)}")
            return None
    
    async def get_ticker(self, exchange_id: str, symbol: str) -> Optional[Dict]:
        """获取行情数据"""
        exchange = self.get_exchange(exchange_id)
        if not exchange:
            return None
        
        try:
            ticker = exchange.fetch_ticker(symbol)
            return ticker
        except Exception as e:
            logger.error(f"获取行情失败 {exchange_id} {symbol}: {str(e)}")
            return None
    
    async def get_order_book(self, exchange_id: str, symbol: str, limit: int = 20) -> Optional[Dict]:
        """获取订单簿"""
        exchange = self.get_exchange(exchange_id)
        if not exchange:
            return None
        
        try:
            order_book = exchange.fetch_order_book(symbol, limit)
            return order_book
        except Exception as e:
            logger.error(f"获取订单簿失败 {exchange_id} {symbol}: {str(e)}")
            return None
    
    async def create_order(self, exchange_id: str, symbol: str, order_type: str,
                          side: str, amount: float, price: float = None,
                          params: Dict = None) -> Optional[Dict]:
        """创建订单"""
        exchange = self.get_exchange(exchange_id)
        if not exchange:
            return None
        
        try:
            # 验证交易对
            if symbol not in exchange.markets:
                logger.error(f"不支持的交易对: {symbol}")
                return None
            
            # 创建订单
            order = exchange.create_order(
                symbol=symbol,
                type=order_type,
                side=side,
                amount=amount,
                price=price,
                params=params or {}
            )
            
            logger.info(f"订单创建成功 {exchange_id}: {order['id']}")
            return order
            
        except Exception as e:
            logger.error(f"创建订单失败 {exchange_id}: {str(e)}")
            return None
    
    async def cancel_order(self, exchange_id: str, order_id: str, 
                          symbol: str = None) -> bool:
        """取消订单"""
        exchange = self.get_exchange(exchange_id)
        if not exchange:
            return False
        
        try:
            result = exchange.cancel_order(order_id, symbol)
            logger.info(f"订单取消成功 {exchange_id}: {order_id}")
            return True
        except Exception as e:
            logger.error(f"取消订单失败 {exchange_id}: {str(e)}")
            return False
    
    async def get_order(self, exchange_id: str, order_id: str, 
                       symbol: str = None) -> Optional[Dict]:
        """获取订单信息"""
        exchange = self.get_exchange(exchange_id)
        if not exchange:
            return None
        
        try:
            order = exchange.fetch_order(order_id, symbol)
            return order
        except Exception as e:
            logger.error(f"获取订单失败 {exchange_id}: {str(e)}")
            return None
    
    async def get_open_orders(self, exchange_id: str, symbol: str = None) -> List[Dict]:
        """获取未完成订单"""
        exchange = self.get_exchange(exchange_id)
        if not exchange:
            return []
        
        try:
            orders = exchange.fetch_open_orders(symbol)
            return orders
        except Exception as e:
            logger.error(f"获取未完成订单失败 {exchange_id}: {str(e)}")
            return []
    
    async def get_trades(self, exchange_id: str, symbol: str = None, 
                        limit: int = 100) -> List[Dict]:
        """获取交易历史"""
        exchange = self.get_exchange(exchange_id)
        if not exchange:
            return []
        
        try:
            trades = exchange.fetch_my_trades(symbol, limit=limit)
            return trades
        except Exception as e:
            logger.error(f"获取交易历史失败 {exchange_id}: {str(e)}")
            return []
    
    def get_market_info(self, exchange_id: str, symbol: str) -> Optional[Dict]:
        """获取市场信息"""
        exchange = self.get_exchange(exchange_id)
        if not exchange or symbol not in exchange.markets:
            return None
        
        return exchange.markets[symbol]
    
    def get_trading_fees(self, exchange_id: str, symbol: str = None) -> Dict:
        """获取交易费率"""
        exchange_info = self.exchange_info.get(exchange_id, {})
        fees = exchange_info.get('fees', {})
        
        if symbol:
            # 根据交易对类型返回相应费率
            if ':' in symbol:  # 期货合约
                return fees.get('futures', {'maker': 0.001, 'taker': 0.001})
            else:  # 现货
                return fees.get('spot', {'maker': 0.001, 'taker': 0.001})
        
        return fees
    
    def validate_order_params(self, exchange_id: str, symbol: str, 
                            side: str, amount: float, price: float = None) -> Tuple[bool, str]:
        """验证订单参数"""
        exchange = self.get_exchange(exchange_id)
        if not exchange:
            return False, "交易所不可用"
        
        # 检查交易对
        if symbol not in exchange.markets:
            return False, f"不支持的交易对: {symbol}"
        
        market = exchange.markets[symbol]
        
        # 检查最小交易量
        min_amount = market.get('limits', {}).get('amount', {}).get('min', 0)
        if amount < min_amount:
            return False, f"交易量低于最小限制: {min_amount}"
        
        # 检查价格精度
        if price:
            price_precision = market.get('precision', {}).get('price', 8)
            if len(str(price).split('.')[-1]) > price_precision:
                return False, f"价格精度超出限制: {price_precision}"
        
        return True, "验证通过"
