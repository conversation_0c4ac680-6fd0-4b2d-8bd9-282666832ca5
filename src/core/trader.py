"""
核心交易模块
整合所有组件，提供统一的交易接口
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import signal
import sys

from .exchange_manager import ExchangeManager
from .order_manager import OrderManager
from ..utils.risk_manager import RiskManager
from ..notifications.telegram_bot import TelegramNotifier
from ..signals.signal_receiver import SignalReceiver
from ..signals.signal_processor import SignalProcessor
from ..utils.logger import get_logger
from ..utils.helpers import load_config, load_env_config

logger = get_logger()


class CryptoTrader:
    """加密货币交易机器人主类"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        # 加载配置
        self.config = load_config(config_path)
        self.env_config = load_env_config()
        
        # 初始化组件
        self.exchange_manager = None
        self.order_manager = None
        self.risk_manager = None
        self.telegram_notifier = None
        self.signal_receiver = None
        self.signal_processor = None
        
        # 运行状态
        self.is_running = False
        self.start_time = None
        
        # 统计信息
        self.daily_stats = {
            'trades': 0,
            'profit': 0.0,
            'loss': 0.0,
            'signals_received': 0
        }
        
        logger.info("交易机器人初始化开始")
        self._initialize_components()
        logger.info("交易机器人初始化完成")
    
    def _initialize_components(self):
        """初始化所有组件"""
        try:
            # 1. 初始化交易所管理器
            exchange_config = load_config("config/exchanges.yaml")
            self.exchange_manager = ExchangeManager(exchange_config, self.env_config)
            
            # 2. 初始化风险管理器
            self.risk_manager = RiskManager(self.config)
            
            # 3. 初始化订单管理器
            self.order_manager = OrderManager(self.exchange_manager, self.config)
            
            # 4. 初始化Telegram通知器
            self.telegram_notifier = TelegramNotifier(self.config, self.env_config)
            
            # 5. 初始化信号处理器
            self.signal_processor = SignalProcessor(
                self.exchange_manager,
                self.order_manager,
                self.risk_manager,
                self.telegram_notifier,
                self.config
            )
            
            # 6. 初始化信号接收器
            self.signal_receiver = SignalReceiver(self.config, self.env_config)
            
            # 7. 连接信号接收器和处理器
            self.signal_receiver.add_signal_callback(self.signal_processor.add_signal)
            
            logger.info("所有组件初始化完成")
            
        except Exception as e:
            logger.error(f"组件初始化失败: {str(e)}")
            raise e
    
    async def start(self):
        """启动交易机器人"""
        if self.is_running:
            logger.warning("交易机器人已在运行")
            return
        
        try:
            self.is_running = True
            self.start_time = datetime.now()
            
            logger.info("🚀 交易机器人启动中...")
            
            # 发送启动通知
            await self.telegram_notifier.send_message(
                "🤖 <b>交易机器人启动</b>\n\n"
                f"⏰ 启动时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
                f"🏪 支持交易所: {', '.join(self.exchange_manager.get_available_exchanges())}\n"
                f"📡 信号接收端口: {self.signal_receiver.port}\n"
                f"🎯 风险管理: 已启用\n"
                f"📊 状态: 运行中"
            )
            
            # 启动定时任务
            asyncio.create_task(self._daily_summary_task())
            asyncio.create_task(self._health_check_task())
            
            # 启动信号接收服务器
            await self.signal_receiver.start_server()
            
        except Exception as e:
            self.is_running = False
            logger.error(f"启动交易机器人失败: {str(e)}")
            await self.telegram_notifier.send_error_notification(
                "启动失败", str(e)
            )
            raise e
    
    async def stop(self):
        """停止交易机器人"""
        if not self.is_running:
            logger.warning("交易机器人未在运行")
            return
        
        try:
            logger.info("🛑 交易机器人停止中...")
            
            self.is_running = False
            
            # 停止信号处理
            await self.signal_processor.stop_processing()
            
            # 停止信号接收服务器
            await self.signal_receiver.stop_server()
            
            # 取消所有未完成订单（可选）
            if self.config.get('trading', {}).get('cancel_orders_on_stop', False):
                await self.order_manager.cancel_all_orders()
            
            # 计算运行时间
            if self.start_time:
                runtime = datetime.now() - self.start_time
                runtime_str = str(runtime).split('.')[0]  # 去掉微秒
            else:
                runtime_str = "未知"
            
            # 发送停止通知
            await self.telegram_notifier.send_message(
                "🛑 <b>交易机器人停止</b>\n\n"
                f"⏰ 停止时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                f"🕐 运行时长: {runtime_str}\n"
                f"📊 今日统计:\n"
                f"  • 交易次数: {self.daily_stats['trades']}\n"
                f"  • 盈利: {self.daily_stats['profit']:.2f} USDT\n"
                f"  • 亏损: {self.daily_stats['loss']:.2f} USDT\n"
                f"  • 信号数量: {self.daily_stats['signals_received']}"
            )
            
            logger.info("交易机器人已停止")
            
        except Exception as e:
            logger.error(f"停止交易机器人失败: {str(e)}")
            raise e
    
    async def _daily_summary_task(self):
        """每日摘要任务"""
        while self.is_running:
            try:
                # 等待到每日摘要时间
                now = datetime.now()
                summary_time = self.config.get('notifications', {}).get('daily_summary_time', '09:00')
                summary_hour, summary_minute = map(int, summary_time.split(':'))
                
                next_summary = now.replace(hour=summary_hour, minute=summary_minute, second=0, microsecond=0)
                if next_summary <= now:
                    next_summary += timedelta(days=1)
                
                wait_seconds = (next_summary - now).total_seconds()
                await asyncio.sleep(wait_seconds)
                
                # 生成并发送每日摘要
                await self._send_daily_summary()
                
            except Exception as e:
                logger.error(f"每日摘要任务错误: {str(e)}")
                await asyncio.sleep(3600)  # 出错后等待1小时
    
    async def _health_check_task(self):
        """健康检查任务"""
        while self.is_running:
            try:
                await asyncio.sleep(300)  # 每5分钟检查一次
                
                # 检查交易所连接
                for exchange_id in self.exchange_manager.get_available_exchanges():
                    balance = await self.exchange_manager.get_balance(exchange_id)
                    if not balance:
                        await self.telegram_notifier.send_error_notification(
                            "交易所连接异常",
                            f"无法连接到 {exchange_id}",
                            {"exchange": exchange_id}
                        )
                
                # 检查风险管理状态
                risk_status = self.risk_manager.get_risk_status()
                if risk_status.get('daily_loss_exceeded'):
                    await self.telegram_notifier.send_risk_alert(
                        "daily_loss_limit",
                        {
                            "message": "已达到每日亏损限制",
                            "current_loss": risk_status.get('daily_loss', 0),
                            "limit": risk_status.get('daily_loss_limit', 0)
                        }
                    )
                
            except Exception as e:
                logger.error(f"健康检查任务错误: {str(e)}")
    
    async def _send_daily_summary(self):
        """发送每日摘要"""
        try:
            # 获取统计数据
            order_summary = self.order_manager.get_order_summary()
            signal_stats = self.signal_processor.get_processing_stats()
            risk_status = self.risk_manager.get_risk_status()
            
            # 计算胜率
            total_trades = signal_stats.get('successful_trades', 0) + signal_stats.get('failed_trades', 0)
            win_rate = (signal_stats.get('successful_trades', 0) / max(total_trades, 1)) * 100
            
            summary_data = {
                'total_pnl': self.daily_stats['profit'] - abs(self.daily_stats['loss']),
                'total_trades': total_trades,
                'successful_trades': signal_stats.get('successful_trades', 0),
                'failed_trades': signal_stats.get('failed_trades', 0),
                'win_rate': win_rate,
                'max_profit': self.daily_stats['profit'],
                'max_loss': self.daily_stats['loss'],
                'active_exchanges': self.exchange_manager.get_available_exchanges()
            }
            
            await self.telegram_notifier.send_daily_summary(summary_data)
            
            # 重置每日统计
            self.daily_stats = {
                'trades': 0,
                'profit': 0.0,
                'loss': 0.0,
                'signals_received': 0
            }
            
        except Exception as e:
            logger.error(f"发送每日摘要失败: {str(e)}")
    
    async def get_status(self) -> Dict:
        """获取机器人状态"""
        if not self.is_running:
            return {"status": "stopped"}
        
        runtime = datetime.now() - self.start_time if self.start_time else timedelta(0)
        
        return {
            "status": "running",
            "start_time": self.start_time.isoformat() if self.start_time else None,
            "runtime": str(runtime).split('.')[0],
            "exchanges": self.exchange_manager.get_available_exchanges(),
            "active_orders": len(self.order_manager.get_active_orders()),
            "daily_stats": self.daily_stats,
            "signal_stats": self.signal_processor.get_processing_stats(),
            "risk_status": self.risk_manager.get_risk_status()
        }
    
    async def manual_trade(self, action: str, symbol: str, amount: float = None,
                          price: float = None, exchange: str = None) -> Dict:
        """手动交易"""
        try:
            signal_data = {
                'action': action,
                'symbol': symbol,
                'amount': amount,
                'price': price,
                'exchange': exchange or self.config['trading']['default_exchange'],
                'strategy': 'manual',
                'signal_id': f"manual_{int(datetime.now().timestamp())}"
            }
            
            await self.signal_processor.add_signal(signal_data)
            
            return {
                'status': 'success',
                'message': '手动交易信号已提交',
                'signal_id': signal_data['signal_id']
            }
            
        except Exception as e:
            logger.error(f"手动交易失败: {str(e)}")
            return {
                'status': 'error',
                'message': str(e)
            }
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"接收到信号 {signum}，准备停止...")
            asyncio.create_task(self.stop())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
