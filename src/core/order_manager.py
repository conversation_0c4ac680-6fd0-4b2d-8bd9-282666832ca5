"""
订单管理模块
负责订单的创建、跟踪、管理和执行
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import uuid

from ..utils.logger import get_logger
from ..utils.helpers import safe_float, get_current_timestamp

logger = get_logger()


class OrderStatus(Enum):
    """订单状态"""
    PENDING = "pending"      # 待提交
    OPEN = "open"           # 已提交，未成交
    PARTIAL = "partial"     # 部分成交
    FILLED = "filled"       # 完全成交
    CANCELED = "canceled"   # 已取消
    REJECTED = "rejected"   # 被拒绝
    EXPIRED = "expired"     # 已过期


class OrderType(Enum):
    """订单类型"""
    MARKET = "market"       # 市价单
    LIMIT = "limit"         # 限价单
    STOP = "stop"          # 止损单
    STOP_LIMIT = "stop_limit"  # 止损限价单


@dataclass
class Order:
    """订单数据结构"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    exchange_id: str = ""
    symbol: str = ""
    side: str = ""          # 'buy' or 'sell'
    type: str = ""          # 订单类型
    amount: float = 0.0
    price: float = 0.0
    filled: float = 0.0     # 已成交数量
    remaining: float = 0.0  # 剩余数量
    status: str = OrderStatus.PENDING.value
    timestamp: datetime = field(default_factory=datetime.now)
    exchange_order_id: str = ""  # 交易所返回的订单ID
    fees: Dict = field(default_factory=dict)
    trades: List[Dict] = field(default_factory=list)
    params: Dict = field(default_factory=dict)
    
    # 策略相关
    strategy: str = "default"
    signal_id: str = ""
    
    # 止损止盈
    stop_loss: float = 0.0
    take_profit: float = 0.0
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'id': self.id,
            'exchange_id': self.exchange_id,
            'symbol': self.symbol,
            'side': self.side,
            'type': self.type,
            'amount': self.amount,
            'price': self.price,
            'filled': self.filled,
            'remaining': self.remaining,
            'status': self.status,
            'timestamp': self.timestamp.isoformat(),
            'exchange_order_id': self.exchange_order_id,
            'fees': self.fees,
            'trades': self.trades,
            'params': self.params,
            'strategy': self.strategy,
            'signal_id': self.signal_id,
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit
        }


class OrderManager:
    """订单管理器"""
    
    def __init__(self, exchange_manager, config: Dict):
        self.exchange_manager = exchange_manager
        self.config = config
        
        # 订单存储
        self.orders: Dict[str, Order] = {}
        self.active_orders: Dict[str, Order] = {}  # 活跃订单
        
        # 配置参数
        self.order_timeout = config.get('orders', {}).get('order_timeout', 300)
        self.retry_attempts = config.get('orders', {}).get('retry_attempts', 3)
        self.price_offset_pct = config.get('orders', {}).get('price_offset_percentage', 0.1)
        
        # 启动订单监控任务
        self._monitoring_task = None
        self._start_monitoring()
        
        logger.info("订单管理器初始化完成")
    
    def _start_monitoring(self):
        """启动订单监控"""
        if self._monitoring_task is None or self._monitoring_task.done():
            self._monitoring_task = asyncio.create_task(self._monitor_orders())
    
    async def _monitor_orders(self):
        """监控订单状态"""
        while True:
            try:
                await self._update_order_status()
                await self._check_order_timeouts()
                await asyncio.sleep(5)  # 每5秒检查一次
            except Exception as e:
                logger.error(f"订单监控错误: {str(e)}")
                await asyncio.sleep(10)
    
    async def _update_order_status(self):
        """更新订单状态"""
        for order_id, order in list(self.active_orders.items()):
            try:
                if not order.exchange_order_id:
                    continue
                
                # 从交易所获取最新订单状态
                exchange_order = await self.exchange_manager.get_order(
                    order.exchange_id, 
                    order.exchange_order_id, 
                    order.symbol
                )
                
                if exchange_order:
                    self._update_order_from_exchange(order, exchange_order)
                    
                    # 如果订单已完成，从活跃订单中移除
                    if order.status in [OrderStatus.FILLED.value, OrderStatus.CANCELED.value, 
                                       OrderStatus.REJECTED.value, OrderStatus.EXPIRED.value]:
                        del self.active_orders[order_id]
                        logger.info(f"订单状态更新: {order_id} -> {order.status}")
                        
            except Exception as e:
                logger.error(f"更新订单状态失败 {order_id}: {str(e)}")
    
    async def _check_order_timeouts(self):
        """检查订单超时"""
        current_time = datetime.now()
        timeout_threshold = timedelta(seconds=self.order_timeout)
        
        for order_id, order in list(self.active_orders.items()):
            if current_time - order.timestamp > timeout_threshold:
                logger.warning(f"订单超时: {order_id}")
                await self.cancel_order(order_id, "订单超时")
    
    def _update_order_from_exchange(self, order: Order, exchange_order: Dict):
        """从交易所数据更新订单"""
        order.filled = safe_float(exchange_order.get('filled', 0))
        order.remaining = safe_float(exchange_order.get('remaining', 0))
        order.status = exchange_order.get('status', order.status)
        
        # 更新费用信息
        if 'fees' in exchange_order:
            order.fees = exchange_order['fees']
        
        # 更新交易记录
        if 'trades' in exchange_order:
            order.trades = exchange_order['trades']
    
    async def create_market_order(self, exchange_id: str, symbol: str, side: str,
                                 amount: float, strategy: str = "default",
                                 signal_id: str = "", **kwargs) -> Optional[Order]:
        """创建市价单"""
        return await self._create_order(
            exchange_id=exchange_id,
            symbol=symbol,
            side=side,
            order_type=OrderType.MARKET.value,
            amount=amount,
            strategy=strategy,
            signal_id=signal_id,
            **kwargs
        )
    
    async def create_limit_order(self, exchange_id: str, symbol: str, side: str,
                                amount: float, price: float, strategy: str = "default",
                                signal_id: str = "", **kwargs) -> Optional[Order]:
        """创建限价单"""
        return await self._create_order(
            exchange_id=exchange_id,
            symbol=symbol,
            side=side,
            order_type=OrderType.LIMIT.value,
            amount=amount,
            price=price,
            strategy=strategy,
            signal_id=signal_id,
            **kwargs
        )
    
    async def _create_order(self, exchange_id: str, symbol: str, side: str,
                           order_type: str, amount: float, price: float = None,
                           strategy: str = "default", signal_id: str = "",
                           **kwargs) -> Optional[Order]:
        """创建订单的内部方法"""
        try:
            # 验证订单参数
            is_valid, error_msg = self.exchange_manager.validate_order_params(
                exchange_id, symbol, side, amount, price
            )
            if not is_valid:
                logger.error(f"订单参数验证失败: {error_msg}")
                return None
            
            # 创建订单对象
            order = Order(
                exchange_id=exchange_id,
                symbol=symbol,
                side=side,
                type=order_type,
                amount=amount,
                price=price or 0.0,
                remaining=amount,
                strategy=strategy,
                signal_id=signal_id,
                stop_loss=kwargs.get('stop_loss', 0.0),
                take_profit=kwargs.get('take_profit', 0.0),
                params=kwargs.get('params', {})
            )
            
            # 调整限价单价格
            if order_type == OrderType.LIMIT.value and not price:
                order.price = await self._calculate_limit_price(exchange_id, symbol, side)
            
            # 提交到交易所
            exchange_order = await self.exchange_manager.create_order(
                exchange_id=exchange_id,
                symbol=symbol,
                order_type=order_type,
                side=side,
                amount=amount,
                price=order.price if order.price > 0 else None,
                params=order.params
            )
            
            if exchange_order:
                order.exchange_order_id = exchange_order['id']
                order.status = exchange_order.get('status', OrderStatus.OPEN.value)
                
                # 存储订单
                self.orders[order.id] = order
                if order.status in [OrderStatus.OPEN.value, OrderStatus.PARTIAL.value]:
                    self.active_orders[order.id] = order
                
                logger.info(f"订单创建成功: {order.id} | {symbol} | {side} | {amount}")
                return order
            else:
                logger.error("提交订单到交易所失败")
                return None
                
        except Exception as e:
            logger.error(f"创建订单失败: {str(e)}")
            return None
    
    async def _calculate_limit_price(self, exchange_id: str, symbol: str, side: str) -> float:
        """计算限价单价格"""
        try:
            ticker = await self.exchange_manager.get_ticker(exchange_id, symbol)
            if not ticker:
                return 0.0
            
            current_price = ticker['last']
            offset = current_price * (self.price_offset_pct / 100)
            
            if side == 'buy':
                # 买单价格略低于当前价格
                return current_price - offset
            else:
                # 卖单价格略高于当前价格
                return current_price + offset
                
        except Exception as e:
            logger.error(f"计算限价单价格失败: {str(e)}")
            return 0.0
    
    async def cancel_order(self, order_id: str, reason: str = "") -> bool:
        """取消订单"""
        if order_id not in self.orders:
            logger.warning(f"订单不存在: {order_id}")
            return False
        
        order = self.orders[order_id]
        
        try:
            success = await self.exchange_manager.cancel_order(
                order.exchange_id, 
                order.exchange_order_id, 
                order.symbol
            )
            
            if success:
                order.status = OrderStatus.CANCELED.value
                if order_id in self.active_orders:
                    del self.active_orders[order_id]
                
                logger.info(f"订单取消成功: {order_id} | 原因: {reason}")
                return True
            else:
                logger.error(f"取消订单失败: {order_id}")
                return False
                
        except Exception as e:
            logger.error(f"取消订单异常: {order_id} | {str(e)}")
            return False
    
    async def cancel_all_orders(self, exchange_id: str = None, symbol: str = None) -> int:
        """取消所有订单"""
        canceled_count = 0
        
        for order_id, order in list(self.active_orders.items()):
            # 过滤条件
            if exchange_id and order.exchange_id != exchange_id:
                continue
            if symbol and order.symbol != symbol:
                continue
            
            if await self.cancel_order(order_id, "批量取消"):
                canceled_count += 1
        
        logger.info(f"批量取消订单完成: {canceled_count} 个")
        return canceled_count
    
    def get_order(self, order_id: str) -> Optional[Order]:
        """获取订单"""
        return self.orders.get(order_id)
    
    def get_orders_by_symbol(self, symbol: str) -> List[Order]:
        """根据交易对获取订单"""
        return [order for order in self.orders.values() if order.symbol == symbol]
    
    def get_orders_by_strategy(self, strategy: str) -> List[Order]:
        """根据策略获取订单"""
        return [order for order in self.orders.values() if order.strategy == strategy]
    
    def get_active_orders(self) -> List[Order]:
        """获取活跃订单"""
        return list(self.active_orders.values())
    
    def get_order_summary(self) -> Dict:
        """获取订单摘要"""
        total_orders = len(self.orders)
        active_orders = len(self.active_orders)
        
        status_counts = {}
        for order in self.orders.values():
            status_counts[order.status] = status_counts.get(order.status, 0) + 1
        
        return {
            'total_orders': total_orders,
            'active_orders': active_orders,
            'status_counts': status_counts,
            'recent_orders': [
                order.to_dict() for order in 
                sorted(self.orders.values(), key=lambda x: x.timestamp, reverse=True)[:10]
            ]
        }
