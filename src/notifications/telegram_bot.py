"""
Telegram通知模块
负责发送交易通知到Telegram
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
import json

try:
    from telegram import Bo<PERSON>
    from telegram.error import TelegramError
    TELEGRAM_AVAILABLE = True
except ImportError:
    TELEGRAM_AVAILABLE = False
    Bot = None
    TelegramError = Exception

from ..utils.logger import get_logger
from ..utils.helpers import format_currency, calculate_percentage_change

logger = get_logger()


class TelegramNotifier:
    """Telegram通知器"""
    
    def __init__(self, config: Dict, env_config: Dict):
        self.config = config.get('notifications', {}).get('telegram', {})
        self.bot_token = env_config.get('TELEGRAM_BOT_TOKEN')
        self.chat_id = env_config.get('TELEGRAM_CHAT_ID')
        
        self.bot = None
        self.enabled = self.config.get('enabled', False) and TELEGRAM_AVAILABLE
        
        if self.enabled:
            if not self.bot_token or not self.chat_id:
                logger.warning("Telegram配置不完整，通知功能已禁用")
                self.enabled = False
            else:
                self.bot = Bot(token=self.bot_token)
                logger.info("Telegram通知器初始化完成")
        else:
            if not TELEGRAM_AVAILABLE:
                logger.warning("python-telegram-bot未安装，Telegram通知功能不可用")
            else:
                logger.info("Telegram通知功能已禁用")
    
    async def send_message(self, message: str, parse_mode: str = 'HTML') -> bool:
        """发送消息"""
        if not self.enabled:
            return False
        
        try:
            await self.bot.send_message(
                chat_id=self.chat_id,
                text=message,
                parse_mode=parse_mode
            )
            return True
        except Exception as e:
            logger.error(f"发送Telegram消息失败: {str(e)}")
            return False
    
    async def send_order_notification(self, order: Dict, action: str) -> bool:
        """发送订单通知"""
        if not self._should_send_notification(f'send_on_order_{action}'):
            return False
        
        try:
            message = self._format_order_message(order, action)
            return await self.send_message(message)
        except Exception as e:
            logger.error(f"发送订单通知失败: {str(e)}")
            return False
    
    async def send_trade_notification(self, trade_data: Dict) -> bool:
        """发送交易通知"""
        if not self._should_send_notification('send_on_order_fill'):
            return False
        
        try:
            message = self._format_trade_message(trade_data)
            return await self.send_message(message)
        except Exception as e:
            logger.error(f"发送交易通知失败: {str(e)}")
            return False
    
    async def send_signal_notification(self, signal_data: Dict) -> bool:
        """发送信号通知"""
        try:
            message = self._format_signal_message(signal_data)
            return await self.send_message(message)
        except Exception as e:
            logger.error(f"发送信号通知失败: {str(e)}")
            return False
    
    async def send_error_notification(self, error_type: str, error_message: str, 
                                    details: Dict = None) -> bool:
        """发送错误通知"""
        if not self._should_send_notification('send_on_error'):
            return False
        
        try:
            message = self._format_error_message(error_type, error_message, details)
            return await self.send_message(message)
        except Exception as e:
            logger.error(f"发送错误通知失败: {str(e)}")
            return False
    
    async def send_daily_summary(self, summary_data: Dict) -> bool:
        """发送每日摘要"""
        if not self._should_send_notification('send_daily_summary'):
            return False
        
        try:
            message = self._format_daily_summary(summary_data)
            return await self.send_message(message)
        except Exception as e:
            logger.error(f"发送每日摘要失败: {str(e)}")
            return False
    
    async def send_risk_alert(self, alert_type: str, alert_data: Dict) -> bool:
        """发送风险警报"""
        try:
            message = self._format_risk_alert(alert_type, alert_data)
            return await self.send_message(message)
        except Exception as e:
            logger.error(f"发送风险警报失败: {str(e)}")
            return False
    
    def _should_send_notification(self, notification_type: str) -> bool:
        """检查是否应该发送通知"""
        return self.enabled and self.config.get(notification_type, True)
    
    def _format_order_message(self, order: Dict, action: str) -> str:
        """格式化订单消息"""
        emoji_map = {
            'create': '📝',
            'fill': '✅',
            'cancel': '❌',
            'partial': '🔄'
        }
        
        emoji = emoji_map.get(action, '📋')
        action_text = {
            'create': '订单创建',
            'fill': '订单成交',
            'cancel': '订单取消',
            'partial': '部分成交'
        }.get(action, '订单更新')
        
        side_emoji = '🟢' if order.get('side') == 'buy' else '🔴'
        side_text = '买入' if order.get('side') == 'buy' else '卖出'
        
        message = f"""
{emoji} <b>{action_text}</b>

{side_emoji} <b>{side_text} {order.get('symbol', 'N/A')}</b>
💰 数量: {order.get('amount', 0):.6f}
💵 价格: {order.get('price', 0):.6f}
🏪 交易所: {order.get('exchange_id', 'N/A').upper()}
📊 类型: {order.get('type', 'N/A').upper()}
🆔 订单ID: <code>{order.get('id', 'N/A')[:8]}...</code>
⏰ 时间: {datetime.now().strftime('%H:%M:%S')}
"""
        
        if order.get('filled', 0) > 0:
            message += f"✅ 已成交: {order.get('filled', 0):.6f}\n"
        
        if order.get('strategy'):
            message += f"🎯 策略: {order.get('strategy')}\n"
        
        return message.strip()
    
    def _format_trade_message(self, trade_data: Dict) -> str:
        """格式化交易消息"""
        side_emoji = '🟢' if trade_data.get('side') == 'buy' else '🔴'
        side_text = '买入' if trade_data.get('side') == 'buy' else '卖出'
        
        pnl = trade_data.get('pnl', 0)
        pnl_emoji = '💰' if pnl > 0 else '💸' if pnl < 0 else '💱'
        pnl_text = f"+{pnl:.2f}" if pnl > 0 else f"{pnl:.2f}"
        
        message = f"""
✅ <b>交易完成</b>

{side_emoji} <b>{side_text} {trade_data.get('symbol', 'N/A')}</b>
💰 数量: {trade_data.get('amount', 0):.6f}
💵 价格: {trade_data.get('price', 0):.6f}
{pnl_emoji} 盈亏: {pnl_text} USDT
🏪 交易所: {trade_data.get('exchange_id', 'N/A').upper()}
⏰ 时间: {datetime.now().strftime('%H:%M:%S')}
"""
        
        if trade_data.get('fees'):
            message += f"💳 手续费: {trade_data['fees'].get('cost', 0):.4f} {trade_data['fees'].get('currency', 'USDT')}\n"
        
        return message.strip()
    
    def _format_signal_message(self, signal_data: Dict) -> str:
        """格式化信号消息"""
        action = signal_data.get('action', '').upper()
        action_emoji = {
            'BUY': '🟢',
            'SELL': '🔴',
            'CLOSE_LONG': '🔄',
            'CLOSE_SHORT': '🔄',
            'CANCEL_ALL': '❌'
        }.get(action, '📡')
        
        message = f"""
📡 <b>交易信号接收</b>

{action_emoji} <b>动作: {action}</b>
📈 交易对: {signal_data.get('symbol', 'N/A')}
💰 数量: {signal_data.get('amount', 'N/A')}
💵 价格: {signal_data.get('price', 'N/A')}
🎯 策略: {signal_data.get('strategy', 'default')}
⏰ 时间: {datetime.now().strftime('%H:%M:%S')}
"""
        
        if signal_data.get('stop_loss'):
            message += f"🛑 止损: {signal_data['stop_loss']}\n"
        
        if signal_data.get('take_profit'):
            message += f"🎯 止盈: {signal_data['take_profit']}\n"
        
        return message.strip()
    
    def _format_error_message(self, error_type: str, error_message: str, 
                            details: Dict = None) -> str:
        """格式化错误消息"""
        message = f"""
🚨 <b>系统错误</b>

❌ 错误类型: {error_type}
📝 错误信息: {error_message}
⏰ 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        if details:
            message += "\n📋 详细信息:\n"
            for key, value in details.items():
                message += f"• {key}: {value}\n"
        
        return message.strip()
    
    def _format_daily_summary(self, summary_data: Dict) -> str:
        """格式化每日摘要"""
        total_pnl = summary_data.get('total_pnl', 0)
        pnl_emoji = '💰' if total_pnl > 0 else '💸' if total_pnl < 0 else '💱'
        pnl_text = f"+{total_pnl:.2f}" if total_pnl > 0 else f"{total_pnl:.2f}"
        
        message = f"""
📊 <b>每日交易摘要</b>

{pnl_emoji} 总盈亏: {pnl_text} USDT
📈 交易次数: {summary_data.get('total_trades', 0)}
✅ 成功交易: {summary_data.get('successful_trades', 0)}
❌ 失败交易: {summary_data.get('failed_trades', 0)}
📊 胜率: {summary_data.get('win_rate', 0):.1f}%
💰 最大盈利: {summary_data.get('max_profit', 0):.2f} USDT
💸 最大亏损: {summary_data.get('max_loss', 0):.2f} USDT
🏪 活跃交易所: {', '.join(summary_data.get('active_exchanges', []))}
⏰ 日期: {datetime.now().strftime('%Y-%m-%d')}
"""
        
        return message.strip()
    
    def _format_risk_alert(self, alert_type: str, alert_data: Dict) -> str:
        """格式化风险警报"""
        alert_emoji = {
            'stop_loss': '🛑',
            'take_profit': '🎯',
            'daily_loss_limit': '⚠️',
            'position_limit': '📊',
            'balance_low': '💰'
        }.get(alert_type, '⚠️')
        
        message = f"""
{alert_emoji} <b>风险警报</b>

🚨 警报类型: {alert_type.replace('_', ' ').title()}
📝 详情: {alert_data.get('message', 'N/A')}
⏰ 时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        if alert_data.get('symbol'):
            message += f"📈 交易对: {alert_data['symbol']}\n"
        
        if alert_data.get('current_value'):
            message += f"📊 当前值: {alert_data['current_value']}\n"
        
        if alert_data.get('threshold'):
            message += f"🎯 阈值: {alert_data['threshold']}\n"
        
        return message.strip()
    
    async def test_connection(self) -> bool:
        """测试Telegram连接"""
        if not self.enabled:
            return False
        
        try:
            test_message = "🤖 交易机器人连接测试成功！"
            return await self.send_message(test_message)
        except Exception as e:
            logger.error(f"Telegram连接测试失败: {str(e)}")
            return False
