"""
主应用程序入口
启动加密货币交易机器人
"""

import asyncio
import argparse
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.core.trader import CryptoTrader
from src.utils.logger import get_logger

logger = get_logger()


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='加密货币交易机器人')
    parser.add_argument(
        '--config', 
        default='config/config.yaml',
        help='配置文件路径 (默认: config/config.yaml)'
    )
    parser.add_argument(
        '--test-telegram',
        action='store_true',
        help='测试Telegram连接'
    )
    parser.add_argument(
        '--status',
        action='store_true',
        help='显示机器人状态'
    )
    parser.add_argument(
        '--manual-trade',
        nargs='+',
        help='手动交易: action symbol [amount] [price] [exchange]'
    )
    
    args = parser.parse_args()
    
    try:
        # 初始化交易机器人
        trader = CryptoTrader(args.config)
        
        # 测试Telegram连接
        if args.test_telegram:
            await test_telegram_connection(trader)
            return
        
        # 显示状态
        if args.status:
            await show_status(trader)
            return
        
        # 手动交易
        if args.manual_trade:
            await manual_trade(trader, args.manual_trade)
            return
        
        # 设置信号处理器
        trader.setup_signal_handlers()
        
        # 启动交易机器人
        logger.info("🚀 启动加密货币交易机器人...")
        await trader.start()
        
    except KeyboardInterrupt:
        logger.info("接收到中断信号，正在停止...")
    except Exception as e:
        logger.error(f"程序运行错误: {str(e)}")
        sys.exit(1)


async def test_telegram_connection(trader: CryptoTrader):
    """测试Telegram连接"""
    logger.info("测试Telegram连接...")
    
    try:
        success = await trader.telegram_notifier.test_connection()
        if success:
            logger.info("✅ Telegram连接测试成功")
        else:
            logger.error("❌ Telegram连接测试失败")
    except Exception as e:
        logger.error(f"Telegram连接测试异常: {str(e)}")


async def show_status(trader: CryptoTrader):
    """显示机器人状态"""
    logger.info("获取机器人状态...")
    
    try:
        status = await trader.get_status()
        
        print("\n" + "="*50)
        print("🤖 交易机器人状态")
        print("="*50)
        
        print(f"状态: {status['status']}")
        
        if status['status'] == 'running':
            print(f"启动时间: {status.get('start_time', 'N/A')}")
            print(f"运行时长: {status.get('runtime', 'N/A')}")
            print(f"支持交易所: {', '.join(status.get('exchanges', []))}")
            print(f"活跃订单: {status.get('active_orders', 0)}")
            
            # 每日统计
            daily_stats = status.get('daily_stats', {})
            print(f"\n📊 今日统计:")
            print(f"  交易次数: {daily_stats.get('trades', 0)}")
            print(f"  盈利: {daily_stats.get('profit', 0):.2f} USDT")
            print(f"  亏损: {daily_stats.get('loss', 0):.2f} USDT")
            print(f"  信号数量: {daily_stats.get('signals_received', 0)}")
            
            # 信号统计
            signal_stats = status.get('signal_stats', {})
            print(f"\n📡 信号统计:")
            print(f"  处理信号: {signal_stats.get('processed_signals', 0)}")
            print(f"  成功交易: {signal_stats.get('successful_trades', 0)}")
            print(f"  失败交易: {signal_stats.get('failed_trades', 0)}")
            print(f"  成功率: {signal_stats.get('success_rate', 0):.1f}%")
            print(f"  队列大小: {signal_stats.get('queue_size', 0)}")
            
            # 风险状态
            risk_status = status.get('risk_status', {})
            print(f"\n⚠️ 风险状态:")
            print(f"  每日亏损: {risk_status.get('daily_loss', 0):.2f} USDT")
            print(f"  亏损限制: {risk_status.get('daily_loss_limit', 0):.2f} USDT")
            print(f"  活跃仓位: {risk_status.get('active_positions', 0)}")
        
        print("="*50)
        
    except Exception as e:
        logger.error(f"获取状态失败: {str(e)}")


async def manual_trade(trader: CryptoTrader, trade_args: list):
    """手动交易"""
    if len(trade_args) < 2:
        logger.error("手动交易参数不足: action symbol [amount] [price] [exchange]")
        return
    
    action = trade_args[0].lower()
    symbol = trade_args[1].upper()
    amount = float(trade_args[2]) if len(trade_args) > 2 and trade_args[2] else None
    price = float(trade_args[3]) if len(trade_args) > 3 and trade_args[3] else None
    exchange = trade_args[4].lower() if len(trade_args) > 4 else None
    
    logger.info(f"执行手动交易: {action} {symbol}")
    
    try:
        result = await trader.manual_trade(action, symbol, amount, price, exchange)
        
        if result['status'] == 'success':
            logger.info(f"✅ {result['message']}")
            logger.info(f"信号ID: {result.get('signal_id')}")
        else:
            logger.error(f"❌ 手动交易失败: {result['message']}")
            
    except Exception as e:
        logger.error(f"手动交易异常: {str(e)}")


def check_environment():
    """检查运行环境"""
    # 检查配置文件
    config_files = [
        'config/config.yaml',
        'config/exchanges.yaml',
        '.env.example'
    ]
    
    missing_files = []
    for file_path in config_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        logger.warning(f"缺少配置文件: {', '.join(missing_files)}")
        logger.info("请确保已正确配置所有必需的文件")
    
    # 检查环境变量
    required_env_vars = [
        'BINANCE_API_KEY',
        'BINANCE_SECRET',
        'TELEGRAM_BOT_TOKEN',
        'TELEGRAM_CHAT_ID'
    ]
    
    missing_env_vars = []
    for env_var in required_env_vars:
        if not os.getenv(env_var):
            missing_env_vars.append(env_var)
    
    if missing_env_vars:
        logger.warning(f"缺少环境变量: {', '.join(missing_env_vars)}")
        logger.info("请在.env文件中配置所有必需的环境变量")
    
    # 检查日志目录
    log_dir = Path('logs')
    if not log_dir.exists():
        log_dir.mkdir(parents=True, exist_ok=True)
        logger.info("创建日志目录: logs/")


def print_banner():
    """打印启动横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                                                              ║
    ║              🤖 加密货币自动交易机器人 🤖                      ║
    ║                                                              ║
    ║                    Crypto Trading Bot                        ║
    ║                                                              ║
    ║  功能特性:                                                    ║
    ║  • 多交易所支持 (Binance, OKX, Bybit)                        ║
    ║  • 外部信号接收和处理                                         ║
    ║  • 智能风险管理                                              ║
    ║  • Telegram实时通知                                          ║
    ║  • 现货和期货交易                                            ║
    ║                                                              ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


if __name__ == "__main__":
    # 打印启动横幅
    print_banner()
    
    # 检查运行环境
    check_environment()
    
    # 运行主程序
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常退出: {str(e)}")
        sys.exit(1)
