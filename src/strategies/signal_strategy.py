"""
信号策略模块
基于外部信号的交易策略
"""

from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta

from .base_strategy import BaseStrategy, StrategySignal, MarketData
from ..utils.logger import get_logger

logger = get_logger()


class SignalStrategy(BaseStrategy):
    """基于外部信号的交易策略"""
    
    def __init__(self, config: Dict):
        super().__init__("SignalStrategy", config)
        
        # 信号策略特有配置
        self.signal_timeout = config.get('signal_timeout', 300)  # 信号超时时间（秒）
        self.min_confidence = config.get('min_confidence', 0.5)  # 最小信号置信度
        self.max_signals_per_hour = config.get('max_signals_per_hour', 10)  # 每小时最大信号数
        self.enable_signal_filtering = config.get('enable_signal_filtering', True)
        
        # 信号过滤配置
        self.price_deviation_threshold = config.get('price_deviation_threshold', 5.0)  # 价格偏差阈值(%)
        self.volume_threshold = config.get('volume_threshold', 1000)  # 最小交易量阈值
        
        # 信号统计
        self.signal_stats = {
            'received': 0,
            'processed': 0,
            'filtered': 0,
            'expired': 0
        }
        
        # 最近信号记录（用于频率控制）
        self.recent_signals = []
        
        logger.info("信号策略初始化完成")
    
    async def analyze_market(self, market_data: List[MarketData]) -> List[StrategySignal]:
        """
        信号策略不主动分析市场，而是等待外部信号
        这个方法主要用于市场数据的预处理和验证
        """
        # 更新市场数据缓存
        self.market_data_cache = {data.symbol: data for data in market_data}
        
        # 清理过期信号
        self._cleanup_expired_signals()
        
        return []  # 信号策略不主动生成信号
    
    async def process_external_signal(self, signal_data: Dict) -> Optional[StrategySignal]:
        """
        处理外部信号
        
        Args:
            signal_data: 外部信号数据
            
        Returns:
            处理后的策略信号，如果信号无效则返回None
        """
        try:
            self.signal_stats['received'] += 1
            
            # 创建策略信号
            signal = StrategySignal(
                action=signal_data.get('action', '').lower(),
                symbol=signal_data.get('symbol', '').upper(),
                amount=signal_data.get('amount'),
                price=signal_data.get('price'),
                stop_loss=signal_data.get('stop_loss'),
                take_profit=signal_data.get('take_profit'),
                confidence=signal_data.get('confidence', 1.0),
                metadata={
                    'source': signal_data.get('source', 'external'),
                    'signal_id': signal_data.get('signal_id'),
                    'strategy_name': signal_data.get('strategy', 'unknown'),
                    'received_at': datetime.now().isoformat()
                }
            )
            
            # 验证信号
            if not self.validate_signal(signal):
                logger.warning(f"信号验证失败: {signal.action} {signal.symbol}")
                return None
            
            # 检查是否应该交易该交易对
            if not self.should_trade_symbol(signal.symbol):
                logger.info(f"跳过不在交易列表中的交易对: {signal.symbol}")
                return None
            
            # 信号过滤
            if self.enable_signal_filtering and not await self._filter_signal(signal):
                self.signal_stats['filtered'] += 1
                logger.info(f"信号被过滤: {signal.action} {signal.symbol}")
                return None
            
            # 频率控制
            if not self._check_signal_frequency():
                logger.warning("信号频率过高，跳过处理")
                return None
            
            # 记录信号
            self.add_signal(signal)
            self.recent_signals.append({
                'signal': signal,
                'timestamp': datetime.now()
            })
            
            self.signal_stats['processed'] += 1
            
            logger.info(f"外部信号处理成功: {signal.action} {signal.symbol} (置信度: {signal.confidence})")
            
            return signal
            
        except Exception as e:
            logger.error(f"处理外部信号失败: {str(e)}")
            return None
    
    async def _filter_signal(self, signal: StrategySignal) -> bool:
        """
        信号过滤逻辑
        
        Args:
            signal: 待过滤的信号
            
        Returns:
            True表示信号通过过滤，False表示被过滤
        """
        try:
            # 置信度过滤
            if signal.confidence < self.min_confidence:
                logger.debug(f"信号置信度过低: {signal.confidence} < {self.min_confidence}")
                return False
            
            # 获取当前市场数据
            market_data = self.market_data_cache.get(signal.symbol)
            if not market_data:
                logger.warning(f"无法获取市场数据: {signal.symbol}")
                return True  # 如果没有市场数据，不过滤
            
            # 价格偏差过滤
            if signal.price:
                current_price = market_data.close
                price_deviation = abs(signal.price - current_price) / current_price * 100
                
                if price_deviation > self.price_deviation_threshold:
                    logger.debug(f"信号价格偏差过大: {price_deviation:.2f}% > {self.price_deviation_threshold}%")
                    return False
            
            # 交易量过滤
            if market_data.volume < self.volume_threshold:
                logger.debug(f"交易量过低: {market_data.volume} < {self.volume_threshold}")
                return False
            
            # 重复信号过滤
            if self._is_duplicate_signal(signal):
                logger.debug(f"重复信号: {signal.action} {signal.symbol}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"信号过滤异常: {str(e)}")
            return True  # 出错时不过滤
    
    def _is_duplicate_signal(self, signal: StrategySignal) -> bool:
        """检查是否为重复信号"""
        # 检查最近5分钟内是否有相同的信号
        cutoff_time = datetime.now() - timedelta(minutes=5)
        
        for recent in self.recent_signals:
            if recent['timestamp'] < cutoff_time:
                continue
            
            recent_signal = recent['signal']
            if (recent_signal.action == signal.action and 
                recent_signal.symbol == signal.symbol and
                abs((recent_signal.price or 0) - (signal.price or 0)) < 0.01):
                return True
        
        return False
    
    def _check_signal_frequency(self) -> bool:
        """检查信号频率是否在允许范围内"""
        # 清理1小时前的信号记录
        cutoff_time = datetime.now() - timedelta(hours=1)
        self.recent_signals = [
            s for s in self.recent_signals 
            if s['timestamp'] > cutoff_time
        ]
        
        # 检查频率
        return len(self.recent_signals) < self.max_signals_per_hour
    
    def _cleanup_expired_signals(self):
        """清理过期信号"""
        cutoff_time = datetime.now() - timedelta(seconds=self.signal_timeout)
        
        expired_count = 0
        self.recent_signals = [
            s for s in self.recent_signals 
            if s['timestamp'] > cutoff_time
        ]
        
        if expired_count > 0:
            self.signal_stats['expired'] += expired_count
            logger.debug(f"清理过期信号: {expired_count} 个")
    
    def get_strategy_info(self) -> Dict:
        """获取策略信息"""
        return {
            'name': self.name,
            'type': 'signal_based',
            'description': '基于外部信号的交易策略',
            'config': {
                'signal_timeout': self.signal_timeout,
                'min_confidence': self.min_confidence,
                'max_signals_per_hour': self.max_signals_per_hour,
                'enable_signal_filtering': self.enable_signal_filtering,
                'price_deviation_threshold': self.price_deviation_threshold,
                'volume_threshold': self.volume_threshold
            },
            'stats': self.signal_stats,
            'performance': self.get_performance_stats(),
            'recent_signals_count': len(self.recent_signals)
        }
    
    def get_signal_stats(self) -> Dict:
        """获取信号统计"""
        return self.signal_stats.copy()
    
    def reset_signal_stats(self):
        """重置信号统计"""
        self.signal_stats = {
            'received': 0,
            'processed': 0,
            'filtered': 0,
            'expired': 0
        }
        self.recent_signals = []
        logger.info("信号统计已重置")
    
    def update_config(self, new_config: Dict):
        """更新策略配置"""
        old_config = {
            'signal_timeout': self.signal_timeout,
            'min_confidence': self.min_confidence,
            'max_signals_per_hour': self.max_signals_per_hour,
            'enable_signal_filtering': self.enable_signal_filtering,
            'price_deviation_threshold': self.price_deviation_threshold,
            'volume_threshold': self.volume_threshold
        }
        
        # 更新配置
        self.signal_timeout = new_config.get('signal_timeout', self.signal_timeout)
        self.min_confidence = new_config.get('min_confidence', self.min_confidence)
        self.max_signals_per_hour = new_config.get('max_signals_per_hour', self.max_signals_per_hour)
        self.enable_signal_filtering = new_config.get('enable_signal_filtering', self.enable_signal_filtering)
        self.price_deviation_threshold = new_config.get('price_deviation_threshold', self.price_deviation_threshold)
        self.volume_threshold = new_config.get('volume_threshold', self.volume_threshold)
        
        logger.info(f"策略配置已更新: {self.name}")
        logger.debug(f"旧配置: {old_config}")
        logger.debug(f"新配置: {new_config}")
    
    def get_active_signals(self) -> List[Dict]:
        """获取活跃信号"""
        cutoff_time = datetime.now() - timedelta(seconds=self.signal_timeout)
        
        active_signals = [
            {
                'action': s['signal'].action,
                'symbol': s['signal'].symbol,
                'price': s['signal'].price,
                'confidence': s['signal'].confidence,
                'timestamp': s['timestamp'].isoformat(),
                'age_seconds': (datetime.now() - s['timestamp']).total_seconds()
            }
            for s in self.recent_signals
            if s['timestamp'] > cutoff_time
        ]
        
        return active_signals
