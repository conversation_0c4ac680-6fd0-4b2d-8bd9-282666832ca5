"""
基础策略模块
定义策略的基础接口和通用功能
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from datetime import datetime
from dataclasses import dataclass

from ..utils.logger import get_logger

logger = get_logger()


@dataclass
class StrategySignal:
    """策略信号数据结构"""
    action: str  # buy, sell, close_long, close_short
    symbol: str
    amount: Optional[float] = None
    price: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    confidence: float = 1.0  # 信号置信度 0-1
    metadata: Dict = None
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
        if self.timestamp is None:
            self.timestamp = datetime.now()


@dataclass
class MarketData:
    """市场数据结构"""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float
    
    def to_dict(self) -> Dict:
        return {
            'symbol': self.symbol,
            'timestamp': self.timestamp.isoformat(),
            'open': self.open,
            'high': self.high,
            'low': self.low,
            'close': self.close,
            'volume': self.volume
        }


class BaseStrategy(ABC):
    """基础策略抽象类"""
    
    def __init__(self, name: str, config: Dict):
        self.name = name
        self.config = config
        self.enabled = config.get('enabled', True)
        
        # 策略参数
        self.symbols = config.get('symbols', [])
        self.timeframe = config.get('timeframe', '1h')
        self.risk_per_trade = config.get('risk_per_trade', 2.0)  # 每笔交易风险百分比
        
        # 策略状态
        self.positions = {}  # 当前持仓
        self.signals_history = []  # 信号历史
        self.performance_stats = {
            'total_signals': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_pnl': 0.0,
            'win_rate': 0.0
        }
        
        logger.info(f"策略初始化: {self.name}")
    
    @abstractmethod
    async def analyze_market(self, market_data: List[MarketData]) -> List[StrategySignal]:
        """
        分析市场数据并生成交易信号
        
        Args:
            market_data: 市场数据列表
            
        Returns:
            交易信号列表
        """
        pass
    
    @abstractmethod
    def get_strategy_info(self) -> Dict:
        """
        获取策略信息
        
        Returns:
            策略信息字典
        """
        pass
    
    def is_enabled(self) -> bool:
        """检查策略是否启用"""
        return self.enabled
    
    def enable(self):
        """启用策略"""
        self.enabled = True
        logger.info(f"策略已启用: {self.name}")
    
    def disable(self):
        """禁用策略"""
        self.enabled = False
        logger.info(f"策略已禁用: {self.name}")
    
    def add_signal(self, signal: StrategySignal):
        """添加信号到历史记录"""
        self.signals_history.append(signal)
        self.performance_stats['total_signals'] += 1
        
        # 保持历史记录在合理范围内
        if len(self.signals_history) > 1000:
            self.signals_history = self.signals_history[-500:]
    
    def update_position(self, symbol: str, side: str, amount: float, price: float):
        """更新持仓信息"""
        if symbol not in self.positions:
            self.positions[symbol] = {
                'long': {'amount': 0.0, 'avg_price': 0.0},
                'short': {'amount': 0.0, 'avg_price': 0.0}
            }
        
        position = self.positions[symbol][side]
        
        if position['amount'] == 0:
            # 新开仓
            position['amount'] = amount
            position['avg_price'] = price
        else:
            # 加仓
            total_value = position['amount'] * position['avg_price'] + amount * price
            total_amount = position['amount'] + amount
            position['avg_price'] = total_value / total_amount if total_amount > 0 else 0
            position['amount'] = total_amount
    
    def close_position(self, symbol: str, side: str, amount: float, price: float) -> float:
        """平仓并计算盈亏"""
        if symbol not in self.positions:
            return 0.0
        
        position = self.positions[symbol][side]
        if position['amount'] <= 0:
            return 0.0
        
        # 计算盈亏
        if side == 'long':
            pnl = (price - position['avg_price']) * min(amount, position['amount'])
        else:  # short
            pnl = (position['avg_price'] - price) * min(amount, position['amount'])
        
        # 更新持仓
        position['amount'] = max(0, position['amount'] - amount)
        if position['amount'] == 0:
            position['avg_price'] = 0.0
        
        # 更新统计
        self.performance_stats['total_pnl'] += pnl
        if pnl > 0:
            self.performance_stats['successful_trades'] += 1
        else:
            self.performance_stats['failed_trades'] += 1
        
        # 更新胜率
        total_trades = self.performance_stats['successful_trades'] + self.performance_stats['failed_trades']
        if total_trades > 0:
            self.performance_stats['win_rate'] = (self.performance_stats['successful_trades'] / total_trades) * 100
        
        return pnl
    
    def get_position(self, symbol: str, side: str) -> Dict:
        """获取持仓信息"""
        if symbol not in self.positions:
            return {'amount': 0.0, 'avg_price': 0.0}
        return self.positions[symbol][side].copy()
    
    def get_all_positions(self) -> Dict:
        """获取所有持仓"""
        return self.positions.copy()
    
    def get_performance_stats(self) -> Dict:
        """获取策略表现统计"""
        return self.performance_stats.copy()
    
    def get_recent_signals(self, limit: int = 10) -> List[Dict]:
        """获取最近的信号"""
        recent_signals = self.signals_history[-limit:] if self.signals_history else []
        return [
            {
                'action': signal.action,
                'symbol': signal.symbol,
                'amount': signal.amount,
                'price': signal.price,
                'confidence': signal.confidence,
                'timestamp': signal.timestamp.isoformat(),
                'metadata': signal.metadata
            }
            for signal in recent_signals
        ]
    
    def calculate_position_size(self, balance: float, price: float, stop_loss: float = None) -> float:
        """计算仓位大小"""
        if not stop_loss or stop_loss <= 0:
            # 如果没有止损，使用固定百分比
            return (balance * self.risk_per_trade / 100) / price
        
        # 基于止损计算仓位大小
        risk_amount = balance * (self.risk_per_trade / 100)
        price_diff = abs(price - stop_loss)
        
        if price_diff > 0:
            return risk_amount / price_diff
        
        return 0.0
    
    def validate_signal(self, signal: StrategySignal) -> bool:
        """验证信号的有效性"""
        # 基础验证
        if not signal.symbol or not signal.action:
            return False
        
        # 检查动作是否有效
        valid_actions = ['buy', 'sell', 'close_long', 'close_short']
        if signal.action not in valid_actions:
            return False
        
        # 检查价格和数量
        if signal.price is not None and signal.price <= 0:
            return False
        
        if signal.amount is not None and signal.amount <= 0:
            return False
        
        # 检查止损止盈
        if signal.stop_loss is not None and signal.stop_loss <= 0:
            return False
        
        if signal.take_profit is not None and signal.take_profit <= 0:
            return False
        
        return True
    
    def should_trade_symbol(self, symbol: str) -> bool:
        """检查是否应该交易该交易对"""
        if not self.symbols:  # 如果没有指定交易对，则交易所有
            return True
        return symbol in self.symbols
    
    def reset_stats(self):
        """重置统计数据"""
        self.performance_stats = {
            'total_signals': 0,
            'successful_trades': 0,
            'failed_trades': 0,
            'total_pnl': 0.0,
            'win_rate': 0.0
        }
        logger.info(f"策略统计已重置: {self.name}")
    
    def to_dict(self) -> Dict:
        """转换为字典格式"""
        return {
            'name': self.name,
            'enabled': self.enabled,
            'symbols': self.symbols,
            'timeframe': self.timeframe,
            'risk_per_trade': self.risk_per_trade,
            'positions': self.positions,
            'performance_stats': self.performance_stats,
            'recent_signals': self.get_recent_signals(5)
        }
