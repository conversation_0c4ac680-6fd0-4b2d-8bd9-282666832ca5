"""
辅助函数模块
提供通用的工具函数
"""

import os
import yaml
import json
from decimal import Decimal, ROUND_DOWN
from typing import Dict, Any, Optional, Union
from datetime import datetime, timezone
import hashlib
import hmac
import base64


def load_config(config_path: str) -> Dict[str, Any]:
    """加载YAML配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as file:
            return yaml.safe_load(file)
    except FileNotFoundError:
        raise FileNotFoundError(f"配置文件未找到: {config_path}")
    except yaml.YAMLError as e:
        raise ValueError(f"配置文件格式错误: {e}")


def load_env_config() -> Dict[str, str]:
    """加载环境变量配置"""
    from dotenv import load_dotenv
    load_dotenv()
    
    return {
        # 交易所配置
        'BINANCE_API_KEY': os.getenv('BINANCE_API_KEY'),
        'BINANCE_SECRET': os.getenv('BINANCE_SECRET'),
        'BINANCE_SANDBOX': os.getenv('BINANCE_SANDBOX', 'true').lower() == 'true',
        
        'OKX_API_KEY': os.getenv('OKX_API_KEY'),
        'OKX_SECRET': os.getenv('OKX_SECRET'),
        'OKX_PASSPHRASE': os.getenv('OKX_PASSPHRASE'),
        'OKX_SANDBOX': os.getenv('OKX_SANDBOX', 'true').lower() == 'true',
        
        # Telegram配置
        'TELEGRAM_BOT_TOKEN': os.getenv('TELEGRAM_BOT_TOKEN'),
        'TELEGRAM_CHAT_ID': os.getenv('TELEGRAM_CHAT_ID'),
        
        # 信号配置
        'SIGNAL_PORT': int(os.getenv('SIGNAL_PORT', 8080)),
        'SIGNAL_SECRET_KEY': os.getenv('SIGNAL_SECRET_KEY'),
        
        # 交易配置
        'DEFAULT_EXCHANGE': os.getenv('DEFAULT_EXCHANGE', 'binance'),
        'DEFAULT_QUOTE_CURRENCY': os.getenv('DEFAULT_QUOTE_CURRENCY', 'USDT'),
        'MAX_POSITION_SIZE': float(os.getenv('MAX_POSITION_SIZE', 1000)),
        'RISK_PERCENTAGE': float(os.getenv('RISK_PERCENTAGE', 2.0)),
        
        # 日志配置
        'LOG_LEVEL': os.getenv('LOG_LEVEL', 'INFO'),
        'LOG_FILE': os.getenv('LOG_FILE', 'logs/trading.log'),
    }


def format_decimal(value: Union[float, str, Decimal], precision: int = 8) -> Decimal:
    """格式化小数，避免浮点数精度问题"""
    if isinstance(value, str):
        decimal_value = Decimal(value)
    else:
        decimal_value = Decimal(str(value))
    
    # 量化到指定精度
    quantized = decimal_value.quantize(
        Decimal('0.' + '0' * precision), 
        rounding=ROUND_DOWN
    )
    return quantized


def calculate_position_size(balance: float, risk_percentage: float, 
                          entry_price: float, stop_loss_price: float) -> float:
    """计算仓位大小"""
    if stop_loss_price <= 0 or entry_price <= 0:
        return 0
    
    # 计算风险金额
    risk_amount = balance * (risk_percentage / 100)
    
    # 计算每单位的风险
    price_diff = abs(entry_price - stop_loss_price)
    risk_per_unit = price_diff
    
    # 计算仓位大小
    if risk_per_unit > 0:
        position_size = risk_amount / risk_per_unit
        return round(position_size, 8)
    
    return 0


def validate_symbol(symbol: str) -> bool:
    """验证交易对格式"""
    if not symbol or '/' not in symbol:
        return False
    
    parts = symbol.split('/')
    if len(parts) != 2:
        return False
    
    base, quote = parts
    if not base or not quote:
        return False
    
    return True


def parse_signal_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """解析信号数据"""
    required_fields = ['action', 'symbol']
    
    # 检查必需字段
    for field in required_fields:
        if field not in data:
            raise ValueError(f"缺少必需字段: {field}")
    
    # 标准化数据
    parsed_data = {
        'action': data['action'].lower(),
        'symbol': data['symbol'].upper(),
        'timestamp': data.get('timestamp', datetime.now(timezone.utc).isoformat()),
        'amount': float(data.get('amount', 0)) if data.get('amount') else None,
        'price': float(data.get('price', 0)) if data.get('price') else None,
        'stop_loss': float(data.get('stop_loss', 0)) if data.get('stop_loss') else None,
        'take_profit': float(data.get('take_profit', 0)) if data.get('take_profit') else None,
        'strategy': data.get('strategy', 'default'),
        'exchange': data.get('exchange', '').lower(),
        'order_type': data.get('order_type', 'market').lower(),
    }
    
    return parsed_data


def generate_signature(secret: str, message: str) -> str:
    """生成HMAC签名"""
    return hmac.new(
        secret.encode('utf-8'),
        message.encode('utf-8'),
        hashlib.sha256
    ).hexdigest()


def verify_signature(secret: str, message: str, signature: str) -> bool:
    """验证HMAC签名"""
    expected_signature = generate_signature(secret, message)
    return hmac.compare_digest(expected_signature, signature)


def format_currency(amount: float, currency: str = "USDT", precision: int = 2) -> str:
    """格式化货币显示"""
    return f"{amount:.{precision}f} {currency}"


def calculate_percentage_change(old_value: float, new_value: float) -> float:
    """计算百分比变化"""
    if old_value == 0:
        return 0
    return ((new_value - old_value) / old_value) * 100


def get_current_timestamp() -> int:
    """获取当前时间戳（毫秒）"""
    return int(datetime.now(timezone.utc).timestamp() * 1000)


def safe_float(value: Any, default: float = 0.0) -> float:
    """安全转换为浮点数"""
    try:
        return float(value) if value is not None else default
    except (ValueError, TypeError):
        return default


def safe_int(value: Any, default: int = 0) -> int:
    """安全转换为整数"""
    try:
        return int(value) if value is not None else default
    except (ValueError, TypeError):
        return default
