"""
日志工具模块
提供统一的日志记录功能
"""

import logging
import logging.handlers
import os
from datetime import datetime
import colorlog
from typing import Optional


class TradingLogger:
    """交易日志记录器"""
    
    def __init__(self, name: str = "TradingBot", log_file: Optional[str] = None, 
                 log_level: str = "INFO", console_output: bool = True):
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # 清除现有的处理器
        self.logger.handlers.clear()
        
        # 创建格式化器
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        console_formatter = colorlog.ColoredFormatter(
            '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s%(reset)s',
            datefmt='%H:%M:%S',
            log_colors={
                'DEBUG': 'cyan',
                'INFO': 'green',
                'WARNING': 'yellow',
                'ERROR': 'red',
                'CRITICAL': 'red,bg_white',
            }
        )
        
        # 控制台输出
        if console_output:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(console_formatter)
            self.logger.addHandler(console_handler)
        
        # 文件输出
        if log_file:
            # 确保日志目录存在
            log_dir = os.path.dirname(log_file)
            if log_dir and not os.path.exists(log_dir):
                os.makedirs(log_dir)
            
            # 使用RotatingFileHandler进行日志轮转
            file_handler = logging.handlers.RotatingFileHandler(
                log_file, maxBytes=10*1024*1024, backupCount=5, encoding='utf-8'
            )
            file_handler.setFormatter(file_formatter)
            self.logger.addHandler(file_handler)
    
    def debug(self, message: str, **kwargs):
        """调试日志"""
        self.logger.debug(message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """信息日志"""
        self.logger.info(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """警告日志"""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """错误日志"""
        self.logger.error(message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """严重错误日志"""
        self.logger.critical(message, **kwargs)
    
    def trade_log(self, action: str, symbol: str, amount: float, price: float, 
                  order_id: str = None, **kwargs):
        """交易专用日志"""
        message = f"TRADE - {action.upper()} {symbol} | Amount: {amount} | Price: {price}"
        if order_id:
            message += f" | OrderID: {order_id}"
        
        for key, value in kwargs.items():
            message += f" | {key}: {value}"
        
        self.info(message)
    
    def signal_log(self, signal_type: str, symbol: str, action: str, **kwargs):
        """信号专用日志"""
        message = f"SIGNAL - {signal_type} | {symbol} | {action.upper()}"
        
        for key, value in kwargs.items():
            message += f" | {key}: {value}"
        
        self.info(message)
    
    def error_log(self, error_type: str, message: str, exception: Exception = None):
        """错误专用日志"""
        error_msg = f"ERROR - {error_type}: {message}"
        if exception:
            error_msg += f" | Exception: {str(exception)}"
        
        self.error(error_msg)


# 全局日志实例
logger = None

def get_logger(name: str = "TradingBot", **kwargs) -> TradingLogger:
    """获取日志实例"""
    global logger
    if logger is None:
        logger = TradingLogger(name, **kwargs)
    return logger

def init_logger(config: dict) -> TradingLogger:
    """初始化日志系统"""
    global logger
    logger = TradingLogger(
        name="TradingBot",
        log_file=config.get('file'),
        log_level=config.get('level', 'INFO'),
        console_output=config.get('console_output', True)
    )
    return logger
