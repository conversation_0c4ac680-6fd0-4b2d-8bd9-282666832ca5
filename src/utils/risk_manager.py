"""
风险管理模块
负责仓位管理、风险控制和资金管理
"""

from typing import Dict, List, Optional, Tuple
from decimal import Decimal
from datetime import datetime, timedelta
import asyncio
from dataclasses import dataclass

from .logger import get_logger
from .helpers import format_decimal, calculate_position_size, safe_float

logger = get_logger()


@dataclass
class Position:
    """持仓信息"""
    symbol: str
    side: str  # 'long' or 'short'
    size: float
    entry_price: float
    current_price: float
    unrealized_pnl: float
    realized_pnl: float
    timestamp: datetime


@dataclass
class RiskLimits:
    """风险限制"""
    max_risk_per_trade: float = 2.0  # 每笔交易最大风险百分比
    max_daily_loss: float = 5.0      # 每日最大亏损百分比
    max_open_positions: int = 5      # 最大同时持仓数量
    max_position_size: float = 1000  # 最大单笔仓位大小
    stop_loss_percentage: float = 3.0 # 默认止损百分比
    take_profit_percentage: float = 6.0 # 默认止盈百分比


class RiskManager:
    """风险管理器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.risk_limits = RiskLimits(**config.get('risk_management', {}))
        
        # 交易记录
        self.positions: Dict[str, Position] = {}
        self.daily_pnl: float = 0.0
        self.daily_trades: int = 0
        self.last_reset_date = datetime.now().date()
        
        # 风险状态
        self.is_trading_halted = False
        self.halt_reason = ""
        
        logger.info("风险管理器初始化完成")
    
    def reset_daily_stats(self):
        """重置每日统计"""
        current_date = datetime.now().date()
        if current_date > self.last_reset_date:
            self.daily_pnl = 0.0
            self.daily_trades = 0
            self.last_reset_date = current_date
            self.is_trading_halted = False
            self.halt_reason = ""
            logger.info("每日风险统计已重置")
    
    def can_open_position(self, symbol: str, side: str, size: float, 
                         current_balance: float) -> Tuple[bool, str]:
        """检查是否可以开仓"""
        self.reset_daily_stats()
        
        # 检查交易是否被暂停
        if self.is_trading_halted:
            return False, f"交易已暂停: {self.halt_reason}"
        
        # 检查每日亏损限制
        if self.daily_pnl < 0:
            daily_loss_percentage = abs(self.daily_pnl) / current_balance * 100
            if daily_loss_percentage >= self.risk_limits.max_daily_loss:
                self.halt_trading("达到每日最大亏损限制")
                return False, "达到每日最大亏损限制"
        
        # 检查最大持仓数量
        if len(self.positions) >= self.risk_limits.max_open_positions:
            return False, "达到最大持仓数量限制"
        
        # 检查单笔仓位大小
        position_value = size * 1  # 假设价格为1，实际应该用当前价格
        if position_value > self.risk_limits.max_position_size:
            return False, "超过最大单笔仓位限制"
        
        # 检查是否已有相同方向的持仓
        position_key = f"{symbol}_{side}"
        if position_key in self.positions:
            return False, f"已存在相同方向的持仓: {symbol} {side}"
        
        return True, "风险检查通过"
    
    def calculate_position_size(self, balance: float, entry_price: float, 
                              stop_loss_price: float, symbol: str) -> float:
        """计算建议仓位大小"""
        if not stop_loss_price or stop_loss_price <= 0:
            # 如果没有止损价格，使用默认止损百分比
            stop_loss_price = entry_price * (1 - self.risk_limits.stop_loss_percentage / 100)
        
        # 使用风险百分比计算仓位大小
        position_size = calculate_position_size(
            balance, 
            self.risk_limits.max_risk_per_trade,
            entry_price,
            stop_loss_price
        )
        
        # 限制最大仓位大小
        max_size = self.risk_limits.max_position_size / entry_price
        position_size = min(position_size, max_size)
        
        logger.info(f"计算仓位大小 - {symbol}: {position_size}")
        return position_size
    
    def add_position(self, symbol: str, side: str, size: float, 
                    entry_price: float, order_id: str = None):
        """添加持仓"""
        position_key = f"{symbol}_{side}"
        position = Position(
            symbol=symbol,
            side=side,
            size=size,
            entry_price=entry_price,
            current_price=entry_price,
            unrealized_pnl=0.0,
            realized_pnl=0.0,
            timestamp=datetime.now()
        )
        
        self.positions[position_key] = position
        self.daily_trades += 1
        
        logger.info(f"添加持仓: {position_key} | 大小: {size} | 入场价: {entry_price}")
    
    def remove_position(self, symbol: str, side: str, exit_price: float, 
                       exit_size: float = None) -> float:
        """移除持仓并计算盈亏"""
        position_key = f"{symbol}_{side}"
        
        if position_key not in self.positions:
            logger.warning(f"未找到持仓: {position_key}")
            return 0.0
        
        position = self.positions[position_key]
        
        # 如果没有指定退出数量，则全部平仓
        if exit_size is None:
            exit_size = position.size
        
        # 计算盈亏
        if side == 'long':
            pnl = (exit_price - position.entry_price) * exit_size
        else:  # short
            pnl = (position.entry_price - exit_price) * exit_size
        
        # 更新统计
        self.daily_pnl += pnl
        position.realized_pnl += pnl
        
        # 如果全部平仓，移除持仓
        if exit_size >= position.size:
            del self.positions[position_key]
            logger.info(f"移除持仓: {position_key} | 盈亏: {pnl:.2f}")
        else:
            # 部分平仓，更新持仓大小
            position.size -= exit_size
            logger.info(f"部分平仓: {position_key} | 剩余: {position.size} | 盈亏: {pnl:.2f}")
        
        return pnl
    
    def update_position_prices(self, price_data: Dict[str, float]):
        """更新持仓的当前价格和未实现盈亏"""
        for position_key, position in self.positions.items():
            symbol = position.symbol
            if symbol in price_data:
                position.current_price = price_data[symbol]
                
                # 计算未实现盈亏
                if position.side == 'long':
                    position.unrealized_pnl = (position.current_price - position.entry_price) * position.size
                else:  # short
                    position.unrealized_pnl = (position.entry_price - position.current_price) * position.size
    
    def check_stop_loss_take_profit(self) -> List[Dict]:
        """检查止损止盈条件"""
        actions = []
        
        for position_key, position in self.positions.items():
            # 计算价格变化百分比
            if position.side == 'long':
                price_change_pct = (position.current_price - position.entry_price) / position.entry_price * 100
            else:  # short
                price_change_pct = (position.entry_price - position.current_price) / position.entry_price * 100
            
            # 检查止损
            if price_change_pct <= -self.risk_limits.stop_loss_percentage:
                actions.append({
                    'action': 'stop_loss',
                    'symbol': position.symbol,
                    'side': position.side,
                    'size': position.size,
                    'current_price': position.current_price,
                    'reason': f'触发止损: {price_change_pct:.2f}%'
                })
            
            # 检查止盈
            elif price_change_pct >= self.risk_limits.take_profit_percentage:
                actions.append({
                    'action': 'take_profit',
                    'symbol': position.symbol,
                    'side': position.side,
                    'size': position.size,
                    'current_price': position.current_price,
                    'reason': f'触发止盈: {price_change_pct:.2f}%'
                })
        
        return actions
    
    def halt_trading(self, reason: str):
        """暂停交易"""
        self.is_trading_halted = True
        self.halt_reason = reason
        logger.warning(f"交易已暂停: {reason}")
    
    def resume_trading(self):
        """恢复交易"""
        self.is_trading_halted = False
        self.halt_reason = ""
        logger.info("交易已恢复")
    
    def get_risk_summary(self) -> Dict:
        """获取风险摘要"""
        total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        
        return {
            'daily_pnl': self.daily_pnl,
            'daily_trades': self.daily_trades,
            'open_positions': len(self.positions),
            'total_unrealized_pnl': total_unrealized_pnl,
            'is_trading_halted': self.is_trading_halted,
            'halt_reason': self.halt_reason,
            'positions': [
                {
                    'symbol': pos.symbol,
                    'side': pos.side,
                    'size': pos.size,
                    'entry_price': pos.entry_price,
                    'current_price': pos.current_price,
                    'unrealized_pnl': pos.unrealized_pnl
                }
                for pos in self.positions.values()
            ]
        }
