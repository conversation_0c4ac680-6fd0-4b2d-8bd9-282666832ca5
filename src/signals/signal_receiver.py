"""
信号接收模块
负责接收来自外部的交易信号
"""

import asyncio
import json
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime
import hashlib
import hmac

from fastapi import FastAPI, HTTPException, Request, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field
import uvicorn

from ..utils.logger import get_logger
from ..utils.helpers import parse_signal_data, verify_signature

logger = get_logger()


class SignalData(BaseModel):
    """信号数据模型"""
    action: str = Field(..., description="交易动作: buy, sell, close_long, close_short, cancel_all")
    symbol: str = Field(..., description="交易对，如 BTC/USDT")
    amount: Optional[float] = Field(None, description="交易数量")
    price: Optional[float] = Field(None, description="交易价格")
    stop_loss: Optional[float] = Field(None, description="止损价格")
    take_profit: Optional[float] = Field(None, description="止盈价格")
    strategy: Optional[str] = Field("default", description="策略名称")
    exchange: Optional[str] = Field(None, description="指定交易所")
    order_type: Optional[str] = Field("market", description="订单类型: market, limit")
    timestamp: Optional[str] = Field(None, description="信号时间戳")
    signature: Optional[str] = Field(None, description="签名验证")


class WebhookSignal(BaseModel):
    """Webhook信号模型"""
    data: SignalData
    source: Optional[str] = Field("webhook", description="信号来源")
    metadata: Optional[Dict] = Field(default_factory=dict, description="元数据")


class SignalReceiver:
    """信号接收器"""
    
    def __init__(self, config: Dict, env_config: Dict):
        self.config = config.get('signals', {})
        self.env_config = env_config
        
        # 服务器配置
        self.host = self.config.get('server', {}).get('host', '0.0.0.0')
        self.port = env_config.get('SIGNAL_PORT', 8080)
        self.secret_key = env_config.get('SIGNAL_SECRET_KEY')
        
        # 支持的动作
        self.supported_actions = self.config.get('supported_actions', [
            'buy', 'sell', 'close_long', 'close_short', 'cancel_all'
        ])
        
        # 回调函数
        self.signal_callbacks: List[Callable] = []
        
        # FastAPI应用
        self.app = FastAPI(title="Crypto Trading Signal Receiver", version="1.0.0")
        self._setup_routes()
        
        # 服务器实例
        self.server = None
        
        logger.info(f"信号接收器初始化完成 - 端口: {self.port}")
    
    def _setup_routes(self):
        """设置API路由"""
        
        @self.app.get("/")
        async def root():
            return {
                "message": "Crypto Trading Signal Receiver",
                "version": "1.0.0",
                "status": "running",
                "supported_actions": self.supported_actions
            }
        
        @self.app.get("/health")
        async def health_check():
            return {"status": "healthy", "timestamp": datetime.now().isoformat()}
        
        @self.app.post("/signal")
        async def receive_signal(signal: WebhookSignal, request: Request):
            """接收交易信号"""
            try:
                # 验证签名（如果配置了密钥）
                if self.secret_key:
                    if not await self._verify_request_signature(request, signal.dict()):
                        raise HTTPException(status_code=401, detail="签名验证失败")
                
                # 验证信号数据
                validated_signal = await self._validate_signal(signal.data)
                
                # 处理信号
                result = await self._process_signal(validated_signal, signal.source, signal.metadata)
                
                return {
                    "status": "success",
                    "message": "信号接收成功",
                    "signal_id": result.get('signal_id'),
                    "timestamp": datetime.now().isoformat()
                }
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"处理信号失败: {str(e)}")
                raise HTTPException(status_code=500, detail=f"处理信号失败: {str(e)}")
        
        @self.app.post("/signal/tradingview")
        async def receive_tradingview_signal(request: Request):
            """接收TradingView信号"""
            try:
                # 获取原始数据
                raw_data = await request.body()
                
                # 验证签名
                if self.secret_key:
                    signature = request.headers.get('X-Signature')
                    if not signature or not verify_signature(self.secret_key, raw_data.decode(), signature):
                        raise HTTPException(status_code=401, detail="签名验证失败")
                
                # 解析JSON数据
                data = json.loads(raw_data)
                
                # 转换为标准格式
                signal_data = self._convert_tradingview_signal(data)
                validated_signal = await self._validate_signal(SignalData(**signal_data))
                
                # 处理信号
                result = await self._process_signal(validated_signal, "tradingview", data)
                
                return {
                    "status": "success",
                    "message": "TradingView信号接收成功",
                    "signal_id": result.get('signal_id')
                }
                
            except Exception as e:
                logger.error(f"处理TradingView信号失败: {str(e)}")
                raise HTTPException(status_code=500, detail=f"处理信号失败: {str(e)}")
        
        @self.app.get("/signals/stats")
        async def get_signal_stats():
            """获取信号统计"""
            return {
                "total_received": getattr(self, '_total_signals', 0),
                "successful": getattr(self, '_successful_signals', 0),
                "failed": getattr(self, '_failed_signals', 0),
                "supported_actions": self.supported_actions
            }
    
    async def _verify_request_signature(self, request: Request, data: Dict) -> bool:
        """验证请求签名"""
        try:
            signature = request.headers.get('X-Signature')
            if not signature:
                return False
            
            # 创建签名字符串
            message = json.dumps(data, sort_keys=True)
            return verify_signature(self.secret_key, message, signature)
            
        except Exception as e:
            logger.error(f"签名验证错误: {str(e)}")
            return False
    
    async def _validate_signal(self, signal: SignalData) -> Dict:
        """验证信号数据"""
        # 检查动作是否支持
        if signal.action.lower() not in self.supported_actions:
            raise ValueError(f"不支持的动作: {signal.action}")
        
        # 检查必需字段
        if signal.action.lower() in ['buy', 'sell'] and not signal.symbol:
            raise ValueError("买卖信号必须包含交易对")
        
        # 解析并标准化数据
        signal_dict = signal.dict()
        validated_data = parse_signal_data(signal_dict)
        
        return validated_data
    
    def _convert_tradingview_signal(self, data: Dict) -> Dict:
        """转换TradingView信号格式"""
        # TradingView信号格式转换
        # 这里需要根据实际的TradingView信号格式进行调整
        
        action_map = {
            'buy': 'buy',
            'sell': 'sell',
            'long': 'buy',
            'short': 'sell',
            'close': 'close_long'  # 需要根据实际情况判断
        }
        
        action = data.get('action', '').lower()
        mapped_action = action_map.get(action, action)
        
        return {
            'action': mapped_action,
            'symbol': data.get('ticker', '').replace('_', '/'),
            'price': data.get('price'),
            'amount': data.get('quantity'),
            'strategy': data.get('strategy', 'tradingview'),
            'timestamp': data.get('time'),
            'order_type': data.get('order_type', 'market')
        }
    
    async def _process_signal(self, signal_data: Dict, source: str, metadata: Dict) -> Dict:
        """处理信号"""
        try:
            # 添加处理信息
            signal_data['source'] = source
            signal_data['metadata'] = metadata
            signal_data['received_at'] = datetime.now().isoformat()
            signal_data['signal_id'] = f"{source}_{int(datetime.now().timestamp())}"
            
            # 记录信号
            logger.info(f"接收到信号: {signal_data['action']} {signal_data['symbol']} from {source}")
            
            # 调用回调函数
            for callback in self.signal_callbacks:
                try:
                    await callback(signal_data)
                except Exception as e:
                    logger.error(f"信号回调执行失败: {str(e)}")
            
            # 更新统计
            self._total_signals = getattr(self, '_total_signals', 0) + 1
            self._successful_signals = getattr(self, '_successful_signals', 0) + 1
            
            return {
                'signal_id': signal_data['signal_id'],
                'status': 'processed'
            }
            
        except Exception as e:
            self._failed_signals = getattr(self, '_failed_signals', 0) + 1
            raise e
    
    def add_signal_callback(self, callback: Callable):
        """添加信号回调函数"""
        self.signal_callbacks.append(callback)
        logger.info(f"添加信号回调函数: {callback.__name__}")
    
    def remove_signal_callback(self, callback: Callable):
        """移除信号回调函数"""
        if callback in self.signal_callbacks:
            self.signal_callbacks.remove(callback)
            logger.info(f"移除信号回调函数: {callback.__name__}")
    
    async def start_server(self):
        """启动信号接收服务器"""
        try:
            config = uvicorn.Config(
                app=self.app,
                host=self.host,
                port=self.port,
                log_level="info"
            )
            self.server = uvicorn.Server(config)
            
            logger.info(f"信号接收服务器启动 - http://{self.host}:{self.port}")
            await self.server.serve()
            
        except Exception as e:
            logger.error(f"启动信号接收服务器失败: {str(e)}")
            raise e
    
    async def stop_server(self):
        """停止信号接收服务器"""
        if self.server:
            self.server.should_exit = True
            logger.info("信号接收服务器已停止")
    
    def get_server_info(self) -> Dict:
        """获取服务器信息"""
        return {
            'host': self.host,
            'port': self.port,
            'supported_actions': self.supported_actions,
            'endpoints': [
                f"http://{self.host}:{self.port}/",
                f"http://{self.host}:{self.port}/signal",
                f"http://{self.host}:{self.port}/signal/tradingview",
                f"http://{self.host}:{self.port}/health"
            ]
        }
