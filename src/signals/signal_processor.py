"""
信号处理模块
负责处理接收到的交易信号并执行相应的交易操作
"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
import uuid

from ..utils.logger import get_logger
from ..utils.helpers import validate_symbol, safe_float

logger = get_logger()


class SignalProcessor:
    """信号处理器"""
    
    def __init__(self, exchange_manager, order_manager, risk_manager, 
                 telegram_notifier, config: Dict):
        self.exchange_manager = exchange_manager
        self.order_manager = order_manager
        self.risk_manager = risk_manager
        self.telegram_notifier = telegram_notifier
        self.config = config
        
        # 处理队列
        self.signal_queue = asyncio.Queue()
        self.processing_task = None
        
        # 统计信息
        self.processed_signals = 0
        self.successful_trades = 0
        self.failed_trades = 0
        
        # 启动信号处理任务
        self._start_processing()
        
        logger.info("信号处理器初始化完成")
    
    def _start_processing(self):
        """启动信号处理任务"""
        if self.processing_task is None or self.processing_task.done():
            self.processing_task = asyncio.create_task(self._process_signals())
    
    async def _process_signals(self):
        """处理信号队列"""
        while True:
            try:
                # 从队列获取信号
                signal_data = await self.signal_queue.get()
                
                # 处理信号
                await self._handle_signal(signal_data)
                
                # 标记任务完成
                self.signal_queue.task_done()
                
            except Exception as e:
                logger.error(f"信号处理错误: {str(e)}")
                await asyncio.sleep(1)
    
    async def add_signal(self, signal_data: Dict):
        """添加信号到处理队列"""
        await self.signal_queue.put(signal_data)
        logger.debug(f"信号已添加到队列: {signal_data.get('action')} {signal_data.get('symbol')}")
    
    async def _handle_signal(self, signal_data: Dict):
        """处理单个信号"""
        try:
            self.processed_signals += 1
            
            # 发送信号通知
            await self.telegram_notifier.send_signal_notification(signal_data)
            
            action = signal_data.get('action', '').lower()
            
            # 根据动作类型处理信号
            if action == 'buy':
                await self._handle_buy_signal(signal_data)
            elif action == 'sell':
                await self._handle_sell_signal(signal_data)
            elif action == 'close_long':
                await self._handle_close_long_signal(signal_data)
            elif action == 'close_short':
                await self._handle_close_short_signal(signal_data)
            elif action == 'cancel_all':
                await self._handle_cancel_all_signal(signal_data)
            else:
                logger.warning(f"未知的信号动作: {action}")
                return
            
            logger.info(f"信号处理完成: {action} {signal_data.get('symbol')}")
            
        except Exception as e:
            logger.error(f"处理信号失败: {str(e)}")
            await self.telegram_notifier.send_error_notification(
                "信号处理错误", 
                str(e), 
                {"signal": signal_data}
            )
    
    async def _handle_buy_signal(self, signal_data: Dict):
        """处理买入信号"""
        symbol = signal_data.get('symbol')
        amount = signal_data.get('amount')
        price = signal_data.get('price')
        exchange_id = signal_data.get('exchange') or self.config['trading']['default_exchange']
        order_type = signal_data.get('order_type', 'market')
        strategy = signal_data.get('strategy', 'default')
        
        # 验证交易对
        if not validate_symbol(symbol):
            logger.error(f"无效的交易对: {symbol}")
            return
        
        try:
            # 获取账户余额
            balance = await self.exchange_manager.get_balance(exchange_id)
            if not balance:
                logger.error(f"无法获取账户余额: {exchange_id}")
                return
            
            # 获取可用余额
            quote_currency = symbol.split('/')[1]
            available_balance = balance.get(quote_currency, {}).get('free', 0)
            
            # 计算交易数量
            if not amount:
                # 如果没有指定数量，根据风险管理计算
                current_price = price
                if not current_price:
                    ticker = await self.exchange_manager.get_ticker(exchange_id, symbol)
                    current_price = ticker['last'] if ticker else 0
                
                if current_price > 0:
                    stop_loss_price = signal_data.get('stop_loss', 0)
                    amount = self.risk_manager.calculate_position_size(
                        available_balance, current_price, stop_loss_price, symbol
                    )
                else:
                    logger.error("无法获取当前价格")
                    return
            
            # 风险检查
            can_trade, reason = self.risk_manager.can_open_position(
                symbol, 'long', amount, available_balance
            )
            if not can_trade:
                logger.warning(f"风险检查失败: {reason}")
                await self.telegram_notifier.send_risk_alert(
                    "position_rejected", 
                    {"symbol": symbol, "reason": reason}
                )
                return
            
            # 创建订单
            if order_type == 'market':
                order = await self.order_manager.create_market_order(
                    exchange_id=exchange_id,
                    symbol=symbol,
                    side='buy',
                    amount=amount,
                    strategy=strategy,
                    signal_id=signal_data.get('signal_id', ''),
                    stop_loss=signal_data.get('stop_loss'),
                    take_profit=signal_data.get('take_profit')
                )
            else:  # limit order
                if not price:
                    logger.error("限价单必须指定价格")
                    return
                
                order = await self.order_manager.create_limit_order(
                    exchange_id=exchange_id,
                    symbol=symbol,
                    side='buy',
                    amount=amount,
                    price=price,
                    strategy=strategy,
                    signal_id=signal_data.get('signal_id', ''),
                    stop_loss=signal_data.get('stop_loss'),
                    take_profit=signal_data.get('take_profit')
                )
            
            if order:
                # 添加到风险管理
                self.risk_manager.add_position(
                    symbol, 'long', amount, price or order.price, order.id
                )
                
                # 发送订单通知
                await self.telegram_notifier.send_order_notification(
                    order.to_dict(), 'create'
                )
                
                self.successful_trades += 1
                logger.info(f"买入订单创建成功: {order.id}")
            else:
                self.failed_trades += 1
                logger.error("买入订单创建失败")
                
        except Exception as e:
            self.failed_trades += 1
            logger.error(f"处理买入信号失败: {str(e)}")
            raise e
    
    async def _handle_sell_signal(self, signal_data: Dict):
        """处理卖出信号"""
        symbol = signal_data.get('symbol')
        amount = signal_data.get('amount')
        price = signal_data.get('price')
        exchange_id = signal_data.get('exchange') or self.config['trading']['default_exchange']
        order_type = signal_data.get('order_type', 'market')
        strategy = signal_data.get('strategy', 'default')
        
        try:
            # 获取账户余额
            balance = await self.exchange_manager.get_balance(exchange_id)
            if not balance:
                logger.error(f"无法获取账户余额: {exchange_id}")
                return
            
            # 获取基础货币余额
            base_currency = symbol.split('/')[0]
            available_balance = balance.get(base_currency, {}).get('free', 0)
            
            # 如果没有指定数量，使用全部可用余额
            if not amount:
                amount = available_balance
            
            if amount <= 0 or amount > available_balance:
                logger.warning(f"卖出数量无效: {amount}, 可用: {available_balance}")
                return
            
            # 创建卖出订单
            if order_type == 'market':
                order = await self.order_manager.create_market_order(
                    exchange_id=exchange_id,
                    symbol=symbol,
                    side='sell',
                    amount=amount,
                    strategy=strategy,
                    signal_id=signal_data.get('signal_id', '')
                )
            else:  # limit order
                if not price:
                    logger.error("限价单必须指定价格")
                    return
                
                order = await self.order_manager.create_limit_order(
                    exchange_id=exchange_id,
                    symbol=symbol,
                    side='sell',
                    amount=amount,
                    price=price,
                    strategy=strategy,
                    signal_id=signal_data.get('signal_id', '')
                )
            
            if order:
                # 计算盈亏（如果有对应的多头仓位）
                pnl = self.risk_manager.remove_position(
                    symbol, 'long', price or order.price, amount
                )
                
                # 发送订单通知
                await self.telegram_notifier.send_order_notification(
                    order.to_dict(), 'create'
                )
                
                # 如果有盈亏，发送交易通知
                if pnl != 0:
                    trade_data = {
                        'symbol': symbol,
                        'side': 'sell',
                        'amount': amount,
                        'price': price or order.price,
                        'pnl': pnl,
                        'exchange_id': exchange_id
                    }
                    await self.telegram_notifier.send_trade_notification(trade_data)
                
                self.successful_trades += 1
                logger.info(f"卖出订单创建成功: {order.id}")
            else:
                self.failed_trades += 1
                logger.error("卖出订单创建失败")
                
        except Exception as e:
            self.failed_trades += 1
            logger.error(f"处理卖出信号失败: {str(e)}")
            raise e
    
    async def _handle_close_long_signal(self, signal_data: Dict):
        """处理平多信号"""
        # 平多实际上就是卖出操作
        signal_data['action'] = 'sell'
        await self._handle_sell_signal(signal_data)
    
    async def _handle_close_short_signal(self, signal_data: Dict):
        """处理平空信号"""
        # 平空实际上就是买入操作
        signal_data['action'] = 'buy'
        await self._handle_buy_signal(signal_data)
    
    async def _handle_cancel_all_signal(self, signal_data: Dict):
        """处理取消所有订单信号"""
        try:
            exchange_id = signal_data.get('exchange')
            symbol = signal_data.get('symbol')
            
            # 取消所有订单
            canceled_count = await self.order_manager.cancel_all_orders(
                exchange_id=exchange_id,
                symbol=symbol
            )
            
            logger.info(f"批量取消订单完成: {canceled_count} 个")
            
            # 发送通知
            await self.telegram_notifier.send_message(
                f"📋 批量取消订单完成\n"
                f"🔢 取消数量: {canceled_count}\n"
                f"🏪 交易所: {exchange_id or '全部'}\n"
                f"📈 交易对: {symbol or '全部'}"
            )
            
        except Exception as e:
            logger.error(f"处理取消所有订单信号失败: {str(e)}")
            raise e
    
    def get_processing_stats(self) -> Dict:
        """获取处理统计"""
        return {
            'processed_signals': self.processed_signals,
            'successful_trades': self.successful_trades,
            'failed_trades': self.failed_trades,
            'success_rate': (self.successful_trades / max(self.processed_signals, 1)) * 100,
            'queue_size': self.signal_queue.qsize()
        }
    
    async def stop_processing(self):
        """停止信号处理"""
        if self.processing_task and not self.processing_task.done():
            self.processing_task.cancel()
            try:
                await self.processing_task
            except asyncio.CancelledError:
                pass
        
        logger.info("信号处理器已停止")
